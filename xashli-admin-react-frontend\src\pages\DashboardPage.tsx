import React from 'react';
import {
  Users,
  CreditCard,
  ArrowUpDown,
  Activity,
  AlertCircle,
  RefreshCw,
  TrendingUp,
} from 'lucide-react';
import { MainLayout } from '../components/layout';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '../components/ui/card';
import { Badge } from '../components/ui/badge';
import { Button } from '../components/ui/button';
import { useAdminDashboard } from '../hooks/useDashboard';
import { formatCurrencyAmount, formatCompactNumber } from '../utils/format';

export const DashboardPage: React.FC = () => {
  const { data, isLoading, isError, error, refetch } = useAdminDashboard();

  console.log('Dashboard data:', data);

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="flex items-center space-x-2">
            <RefreshCw className="h-6 w-6 animate-spin text-blue-600" />
            <span className="text-lg text-gray-600">Loading dashboard...</span>
          </div>
        </div>
      </MainLayout>
    );
  }

  if (isError) {
    return (
      <MainLayout>
        <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
          <AlertCircle className="h-12 w-12 text-red-500" />
          <div className="text-center">
            <h2 className="text-xl font-semibold text-gray-900 mb-2">
              Failed to load dashboard
            </h2>
            <p className="text-gray-600 mb-4">
              {error?.message || 'An unexpected error occurred'}
            </p>
            <Button onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </div>
      </MainLayout>
    );
  }

  const dashboardData = data?.data;

  if (!dashboardData) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-[400px]">
          <p className="text-gray-600">No dashboard data available</p>
        </div>
      </MainLayout>
    );
  }

  const getStatusColor = (status: string, count: number) => {
    if (count === 0) return 'outline';
    switch (status) {
      case 'pending':
        return 'default';
      case 'matched':
      case 'paid':
        return 'secondary';
      case 'completed':
      case 'confirmed':
        return 'destructive';
      case 'cancelled':
      case 'disputed':
        return 'outline';
      default:
        return 'outline';
    }
  };

  return (
    <MainLayout>
      {/* Welcome Section */}
      <div className="mb-8">
        <h2 className="text-3xl font-bold text-brand-black mb-2 flex items-center gap-3">
          <TrendingUp className="h-8 w-8 text-blue-600" />
          Admin Dashboard
        </h2>
        <p className="text-brand-grey-600">
          Overview of your Xashli platform performance and key metrics.
        </p>
      </div>

      {/* Overview Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {/* Total Users */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Users
            </CardTitle>
            <Users className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-gray-900">
              {formatCompactNumber(dashboardData.users.counts.total)}
            </div>
            <p className="text-xs text-green-600 mt-1">
              +{dashboardData.users.counts.new_today} today
            </p>
          </CardContent>
        </Card>

        {/* Total Funds */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Funds
            </CardTitle>
            <CreditCard className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Naira:</span>
                <span className="text-lg font-bold text-gray-900">
                  {formatCurrencyAmount(
                    dashboardData.funds.amounts.fiat,
                    'fiat'
                  )}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">SOL:</span>
                <span className="text-lg font-bold text-gray-900">
                  {formatCurrencyAmount(
                    dashboardData.funds.amounts.crypto,
                    'crypto'
                  )}
                </span>
              </div>
            </div>
            <p className="text-xs text-green-600 mt-3">
              +{dashboardData.funds.counts.new_today} new today
            </p>
          </CardContent>
        </Card>

        {/* Total Withdrawals */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Withdrawals
            </CardTitle>
            <ArrowUpDown className="h-4 w-4 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Naira:</span>
                <span className="text-lg font-bold text-gray-900">
                  {formatCurrencyAmount(
                    dashboardData.withdraws.amounts.fiat,
                    'fiat'
                  )}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">SOL:</span>
                <span className="text-lg font-bold text-gray-900">
                  {formatCurrencyAmount(
                    dashboardData.withdraws.amounts.crypto,
                    'crypto'
                  )}
                </span>
              </div>
            </div>
            <p className="text-xs text-gray-600 mt-3">
              {formatCompactNumber(dashboardData.withdraws.counts.total)} total
              requests
            </p>
          </CardContent>
        </Card>

        {/* Total Matches */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-gray-600">
              Total Matches
            </CardTitle>
            <Activity className="h-4 w-4 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">Naira:</span>
                <span className="text-lg font-bold text-gray-900">
                  {formatCurrencyAmount(
                    dashboardData.matches.amounts.fiat,
                    'fiat'
                  )}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">SOL:</span>
                <span className="text-lg font-bold text-gray-900">
                  {formatCurrencyAmount(
                    dashboardData.matches.amounts.crypto,
                    'crypto'
                  )}
                </span>
              </div>
            </div>
            <p className="text-xs text-green-600 mt-3">
              {formatCompactNumber(dashboardData.matches.counts.total)} total
              matches
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* User Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900">
              User Statistics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.users.counts.total.toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">New Today</p>
                <p className="text-2xl font-bold text-green-600">
                  {dashboardData.users.counts.new_today.toLocaleString()}
                </p>
              </div>
            </div>
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">
                User Breakdown
              </h4>
              <div className="flex flex-wrap gap-2">
                <Badge
                  variant="secondary"
                  className="bg-green-100 text-green-800"
                >
                  Active: {dashboardData.users.counts.active}
                </Badge>
                <Badge
                  variant="secondary"
                  className="bg-gray-100 text-gray-800"
                >
                  Inactive: {dashboardData.users.counts.inactive}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fund Management */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900">
              Fund Management
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.funds.counts.total.toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">New Today</p>
                <p className="text-2xl font-bold text-green-600">
                  {dashboardData.funds.counts.new_today.toLocaleString()}
                </p>
              </div>
            </div>
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">
                Status Breakdown
              </h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(dashboardData.funds.counts.statuses).map(
                  ([status, count]) => (
                    <Badge
                      key={status}
                      variant={getStatusColor(status, Number(count))}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}:{' '}
                      {Number(count)}
                    </Badge>
                  )
                )}
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Fiat Amount:</span>
                <span className="font-medium">
                  {formatCurrencyAmount(
                    dashboardData.funds.amounts.fiat,
                    'fiat'
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Crypto Amount:</span>
                <span className="font-medium">
                  {formatCurrencyAmount(
                    dashboardData.funds.amounts.crypto,
                    'crypto'
                  )}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Withdrawal Statistics */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900">
              Withdrawal Statistics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.withdraws.counts.total.toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">New Today</p>
                <p className="text-2xl font-bold text-green-600">
                  {dashboardData.withdraws.counts.new_today.toLocaleString()}
                </p>
              </div>
            </div>
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">
                Status Breakdown
              </h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(dashboardData.withdraws.counts.statuses).map(
                  ([status, count]) => (
                    <Badge
                      key={status}
                      variant={getStatusColor(status, Number(count))}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}:{' '}
                      {Number(count)}
                    </Badge>
                  )
                )}
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Fiat Withdrawals:</span>
                <span className="font-medium">
                  {formatCurrencyAmount(
                    dashboardData.withdraws.amounts.fiat,
                    'fiat'
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Crypto Withdrawals:</span>
                <span className="font-medium">
                  {formatCurrencyAmount(
                    dashboardData.withdraws.amounts.crypto,
                    'crypto'
                  )}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Match Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold text-gray-900">
              Match Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm text-gray-600">Total</p>
                <p className="text-2xl font-bold text-gray-900">
                  {dashboardData.matches.counts.total.toLocaleString()}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-600">New Today</p>
                <p className="text-2xl font-bold text-green-600">
                  {dashboardData.matches.counts.new_today.toLocaleString()}
                </p>
              </div>
            </div>
            <div className="space-y-3">
              <h4 className="text-sm font-medium text-gray-700">
                Status Breakdown
              </h4>
              <div className="flex flex-wrap gap-2">
                {Object.entries(dashboardData.matches.counts.statuses).map(
                  ([status, count]) => (
                    <Badge
                      key={status}
                      variant={getStatusColor(status, Number(count))}
                    >
                      {status.charAt(0).toUpperCase() + status.slice(1)}:{' '}
                      {Number(count)}
                    </Badge>
                  )
                )}
              </div>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Fiat Matches:</span>
                <span className="font-medium">
                  {formatCurrencyAmount(
                    dashboardData.matches.amounts.fiat,
                    'fiat'
                  )}
                </span>
              </div>
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Crypto Matches:</span>
                <span className="font-medium">
                  {formatCurrencyAmount(
                    dashboardData.matches.amounts.crypto,
                    'crypto'
                  )}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  );
};
