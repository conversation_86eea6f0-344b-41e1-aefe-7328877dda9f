<?php

namespace App\Http\Controllers;

use App\Models\AdminActivityLog;
use App\Models\Fund;
use App\Models\PaymentMatch;
use App\Models\PlatformFee;
use App\Models\PlatformSetting;
use App\Models\ReferralBonus;
use App\Models\User;
use App\Models\UserStat;
use App\Models\Withdraw;
use App\Services\PaymentMatchingService;
use App\Services\PaymentMatchTimeoutService;
use App\Services\PaystackService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;

class PaymentMatchController extends Controller
{
    protected $paymentMatchingService;

    protected $paymentMatchTimeoutService;

    protected $paystackService;

    public function __construct(PaymentMatchingService $paymentMatchingService, PaymentMatchTimeoutService $paymentMatchTimeoutService, PaystackService $paystackService)
    {
        $this->paymentMatchingService = $paymentMatchingService;
        $this->paymentMatchTimeoutService = $paymentMatchTimeoutService;
        $this->paystackService = $paystackService;
    }

    /**
     * Display a listing of payment matches.
     * Admin can see all matches, users can only see their own.
     */
    public function index(Request $request): JsonResponse
    {
        $user = auth()->user();
        $isAdmin = $user->role === 'admin';

        $query = PaymentMatch::with([
            'fund' => function ($query) {
                $query->select('id', 'user_id', 'amount', 'currency', 'status', 'created_at', 'payment_method_id');
            },
            'withdraw' => function ($query) {
                $query->select('id', 'user_id', 'fund_id', 'base_withdrawable_amount', 'total_withdrawable_amount', 'amount_matched', 'status', 'created_at', 'payment_method_id')
                    ->with(['fund:id,currency']);
            },
            'fundUser:id,full_name,email,phone',
            'withdrawUser:id,full_name,email,phone',
            'paymentMethod:id,type,bank_name,account_number,account_name,wallet_address,crypto_network',
            'disputes:id,payment_match_id,dispute_user_id,status,reason,disputed_at',
        ]);

        // If not admin, filter to only user's own matches
        if (! $isAdmin) {
            $query->where(function ($q) use ($user) {
                $q->where('fund_user_id', $user->id)
                    ->orWhere('withdraw_user_id', $user->id);
            });
        }

        // Filter by status if provided
        if ($request->has('status') && ! empty($request->status)) {
            $query->where('status', $request->status);
        }

        // Filter by initiator if provided (admin only)
        if ($isAdmin && $request->has('initiator') && ! empty($request->initiator)) {
            $query->where('initiator', $request->initiator);
        }

        // Filter by currency if provided
        if ($request->has('currency') && ! empty($request->currency)) {
            $query->whereHas('fund', function ($q) use ($request) {
                $q->where('currency', $request->currency);
            });
        }

        // Filter by amount range if provided (admin only)
        if ($isAdmin && $request->has('min_amount') && is_numeric($request->min_amount)) {
            $query->where('amount', '>=', $request->min_amount);
        }
        if ($isAdmin && $request->has('max_amount') && is_numeric($request->max_amount)) {
            $query->where('amount', '<=', $request->max_amount);
        }

        // Filter by date range if provided
        if ($request->has('start_date') && ! empty($request->start_date)) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->has('end_date') && ! empty($request->end_date)) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Filter by user (fund or withdraw owner) if provided (admin only)
        if ($isAdmin && $request->has('user_id') && ! empty($request->user_id)) {
            $query->where(function ($q) use ($request) {
                $q->where('fund_user_id', $request->user_id)
                    ->orWhere('withdraw_user_id', $request->user_id);
            });
        }

        // Filter by specific fund ID if provided (admin only)
        if ($isAdmin && $request->has('fund_id') && ! empty($request->fund_id)) {
            $query->where('fund_id', $request->fund_id);
        }

        // Filter by specific withdraw ID if provided (admin only)
        if ($isAdmin && $request->has('withdraw_id') && ! empty($request->withdraw_id)) {
            $query->where('withdraw_id', $request->withdraw_id);
        }

        // Search by match ID, fund ID, or withdraw ID if provided
        if ($request->has('search') && ! empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('id', 'like', "%{$searchTerm}%")
                    ->orWhere('fund_id', 'like', "%{$searchTerm}%")
                    ->orWhere('withdraw_id', 'like', "%{$searchTerm}%");
            });
        }

        // Sort by created_at in descending order by default
        $sortBy = $request->get('sort_by', 'created_at');
        $sortOrder = $request->get('sort_order', 'desc');

        // Validate sort parameters
        $allowedSortFields = $isAdmin
            ? ['created_at', 'updated_at', 'amount', 'status', 'initiator']
            : ['created_at', 'updated_at', 'amount', 'status'];

        if (in_array($sortBy, $allowedSortFields) && in_array($sortOrder, ['asc', 'desc'])) {
            $query->orderBy($sortBy, $sortOrder);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        // Pagination
        $perPage = min($request->get('per_page', 15), $isAdmin ? 100 : 50);
        $paginatedPaymentMatches = $query->paginate($perPage);

        // Add user context for non-admin users and hide sensitive info
        if (! $isAdmin) {
            $paginatedPaymentMatches->getCollection()->transform(function ($match) use ($user) {
                $match->user_role = null;
                if ($match->fund_user_id === $user->id) {
                    $match->user_role = 'funder';
                } elseif ($match->withdraw_user_id === $user->id) {
                    $match->user_role = 'withdrawer';
                }

                // Hide other user's sensitive information
                if ($match->fund && $match->fund_user_id !== $user->id) {
                    $match->fund->user = null;
                }
                if ($match->withdraw && $match->withdraw_user_id !== $user->id) {
                    $match->withdraw->user = null;
                }

                return $match;
            });
        }

        $response = [
            'paymentMatches' => $paginatedPaymentMatches->items(),
            'pagination' => [
                'current_page' => $paginatedPaymentMatches->currentPage(),
                'last_page' => $paginatedPaymentMatches->lastPage(),
                'per_page' => $paginatedPaymentMatches->perPage(),
                'total' => $paginatedPaymentMatches->total(),
                'from' => $paginatedPaymentMatches->firstItem(),
                'to' => $paginatedPaymentMatches->lastItem(),
            ],
        ];

        return $this->success($response, 'Payment matches retrieved successfully');
    }

    /**
     * Display a specific payment match.
     * Admin can see any match, users can only see their own.
     */
    public function show(string $id): JsonResponse
    {
        $user = auth()->user();
        $isAdmin = $user->role === 'admin';

        $query = PaymentMatch::with([
            'fund' => function ($query) {
                $query->select('id', 'user_id', 'amount', 'currency', 'status', 'created_at', 'payment_method_id');
            },
            'withdraw' => function ($query) {
                $query->select('id', 'user_id', 'fund_id', 'base_withdrawable_amount', 'total_withdrawable_amount', 'amount_matched', 'status', 'created_at', 'payment_method_id')
                    ->with(['fund:id,currency']);
            },
            'fundUser:id,full_name,email,phone',
            'withdrawUser:id,full_name,email,phone',
            'paymentMethod:id,type,bank_name,account_number,account_name,wallet_address,crypto_network',
            'disputes' => function ($query) use ($isAdmin) {
                if ($isAdmin) {
                    $query->with(['disputeUser:id,full_name,email', 'resolveUser:id,full_name,email']);
                } else {
                    $query->select('id', 'payment_match_id', 'dispute_user_id', 'status', 'reason', 'disputed_at')
                        ->with(['disputeUser:id,full_name,email']);
                }
            },
        ]);

        // If not admin, filter to only user's own matches
        if (! $isAdmin) {
            $query->where(function ($q) use ($user) {
                $q->where('fund_user_id', $user->id)
                    ->orWhere('withdraw_user_id', $user->id);
            });
        }

        $match = $query->find($id);

        if (! $match) {
            $message = $isAdmin
                ? 'Payment match not found'
                : 'Payment match not found or you do not have access to it';

            return $this->notFound($message);
        }

        // Add user context for non-admin users and hide sensitive info
        if (! $isAdmin) {
            $match->user_role = null;
            if ($match->fund_user_id === $user->id) {
                $match->user_role = 'funder';
            } elseif ($match->withdraw_user_id === $user->id) {
                $match->user_role = 'withdrawer';
            }

            // Hide sensitive information from other users
            if ($match->fund && $match->fund_user_id !== $user->id) {
                $match->fund->user = null;
            }
            if ($match->withdraw && $match->withdraw_user_id !== $user->id) {
                $match->withdraw->user = null;
            }
        }

        return $this->success($match, 'Payment match retrieved successfully');
    }

    /**
     * Get payment match statistics.
     * Admins see platform-wide statistics, users see their own statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            // Check if user is admin to determine query scope
            if (auth()->user()->isAdmin()) {
                // Admin gets platform-wide statistics
                $query = PaymentMatch::query();

                // Allow admin to filter by user_id if provided
                if ($request->has('user_id') && ! empty($request->user_id)) {
                    $query->where(function ($q) use ($request) {
                        $q->where('fund_user_id', $request->user_id)
                            ->orWhere('withdraw_user_id', $request->user_id);
                    });
                }

                // Allow admin to filter by user email if provided
                if ($request->has('email') && ! empty($request->email)) {
                    $query->where(function ($q) use ($request) {
                        $q->whereHas('fundUser', function ($query) use ($request) {
                            $query->where('email', 'like', '%' . $request->email . '%');
                        })->orWhereHas('withdrawUser', function ($query) use ($request) {
                            $query->where('email', 'like', '%' . $request->email . '%');
                        });
                    });
                }
            } else {
                // Users only see their own statistics
                $userId = auth()->id();
                $query = PaymentMatch::where(function ($q) use ($userId) {
                    $q->where('fund_user_id', $userId)
                        ->orWhere('withdraw_user_id', $userId);
                });
            }

            // Optional date range filtering
            if ($request->has('date_from') && ! empty($request->date_from)) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && ! empty($request->date_to)) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $matches = $query->with(['fund'])->get();

            // Group matches by status for efficient calculations
            $matchesByStatus = $matches->groupBy('status');

            // Initialize status groups to ensure all statuses are represented
            $statuses = ['pending', 'paid', 'confirmed', 'disputed'];
            foreach ($statuses as $status) {
                if (! $matchesByStatus->has($status)) {
                    $matchesByStatus[$status] = collect();
                }
            }

            // Helper function to calculate improved status stats
            $calculateStatusStats = function ($statusMatches) {
                $fiatMatches = $statusMatches->filter(fn ($m) => $m->fund && $m->fund->currency === 'fiat');
                $cryptoMatches = $statusMatches->filter(fn ($m) => $m->fund && $m->fund->currency === 'crypto');

                return [
                    'total_count' => $statusMatches->count(),
                    'count' => [
                        'fiat' => $fiatMatches->count(),
                        'crypto' => $cryptoMatches->count(),
                    ],
                    'amount' => [
                        'fiat' => round($fiatMatches->sum('amount'), 2),
                        'crypto' => round($cryptoMatches->sum('amount'), 6),
                    ],
                ];
            };

            // Calculate overall statistics
            $overallStats = $calculateStatusStats($matches);

            // Get dispute statistics
            $disputeQuery = clone $query;
            if (! auth()->user()->isAdmin()) {
                $userId = auth()->id();
                $disputeQuery = PaymentMatch::where(function ($q) use ($userId) {
                    $q->where('fund_user_id', $userId)
                        ->orWhere('withdraw_user_id', $userId);
                });
            }

            $disputeMatches = $disputeQuery->whereHas('disputes')->with(['disputes'])->get();

            $statistics = [
                'overview' => [
                    'total_count' => $overallStats['total_count'],
                    'count' => $overallStats['count'],
                    'amount' => $overallStats['amount'],
                ],
                'statuses' => [
                    'pending' => $calculateStatusStats($matchesByStatus['pending']),
                    'paid' => $calculateStatusStats($matchesByStatus['paid']),
                    'confirmed' => $calculateStatusStats($matchesByStatus['confirmed']),
                    'disputed' => $calculateStatusStats($matchesByStatus['disputed']),
                ],
                'initiators' => [
                    'auto_count' => $matches->where('initiator', 'auto')->count(),
                    'manual_count' => $matches->where('initiator', 'manual')->count(),
                ],
                'disputes' => [
                    'total_count' => $disputeMatches->count(),
                    'pending_count' => $disputeMatches->filter(fn ($m) => $m->dispute && $m->dispute->status === 'pending')->count(),
                    'under_review_count' => $disputeMatches->filter(fn ($m) => $m->dispute && $m->dispute->status === 'under_review')->count(),
                    'resolved_count' => $disputeMatches->filter(fn ($m) => $m->dispute && $m->dispute->status === 'resolved')->count(),
                ],
            ];

            return $this->success($statistics, 'Payment match statistics retrieved successfully');
        } catch (\Exception $e) {
            return $this->serverError('Failed to retrieve payment match statistics: ' . $e->getMessage());
        }
    }

    /**
     * Manually match a fund with a withdraw.
     */
    public function manualMatch(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'fund_id' => 'required|uuid|exists:funds,id',
            'withdraw_id' => 'required|uuid|exists:withdraws,id',
            'amount' => 'required|numeric|min:0.01',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $fund = Fund::find($request->fund_id);
        $withdraw = Withdraw::with(['paymentMethod'])->find($request->withdraw_id);

        if (! $fund || ! $withdraw) {
            return $this->notFound('Fund or withdraw not found');
        }

        // Get withdraw user for role check
        $withdrawUser = User::find($withdraw->user_id);

        // Check if fund is in a matchable state
        if ($fund->status !== 'pending') {
            return $this->error('Only pending funds can be matched', 400);
        }

        // Check if withdraw is in a matchable state
        if ($withdraw->status !== 'pending') {
            return $this->error('Only pending withdraws can be matched', 400);
        }

        // Check if currencies match
        if ($fund->currency !== $withdraw->fund->currency) {
            return $this->error('Fund and withdraw currencies must match', 400);
        }

        // Check if payment method type matches the currency
        if (($fund->currency === 'fiat' && $withdraw->paymentMethod->type !== 'bank') ||
            ($fund->currency === 'crypto' && $withdraw->paymentMethod->type !== 'crypto')) {
            return $this->error('Payment method type does not match the currency', 400);
        }

        // Check if the amount is valid
        $remainingWithdrawAmount = $withdraw->total_withdrawable_amount - $withdraw->amount_matched;
        $remainingFundAmount = $fund->amount - $fund->amount_matched;

        if ($request->amount > $remainingWithdrawAmount) {
            return $this->error('Match amount exceeds the remaining withdraw amount', 400);
        }

        if ($request->amount > $remainingFundAmount) {
            return $this->error('Match amount exceeds the remaining fund amount', 400);
        }

        try {
            DB::beginTransaction();

            // Create the match
            $match = PaymentMatch::create([
                'fund_id' => $fund->id,
                'withdraw_id' => $withdraw->id,
                'fund_user_id' => $fund->user_id,
                'withdraw_user_id' => $withdraw->user_id,
                'amount' => $request->amount,
                'payment_method_id' => $withdraw->payment_method_id,
                'initiator' => 'manual',
                'status' => 'pending',
                'is_admin_withdraw' => $withdrawUser->role === 'admin',
            ]);

            // Update fund status and amount matched
            $fund->amount_matched = $fund->amount_matched + $request->amount;
            if ($fund->amount_matched >= $fund->amount) {
                $fund->status = 'matched';
            }
            $fund->save();

            // Update withdraw status and amount matched
            $withdraw->amount_matched = $withdraw->amount_matched + $request->amount;
            if ($withdraw->amount_matched >= $withdraw->total_withdrawable_amount) {
                $withdraw->status = 'matched';
            }
            $withdraw->save();

            // Log the activity
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_PAYMENT_MANUALLY_MATCHED,
                null,
                [
                    'fund_id' => $fund->id,
                    'withdraw_id' => $withdraw->id,
                    'amount' => $request->amount,
                    'match_id' => $match->id,
                ]
            );

            DB::commit();

            return $this->success($match, 'Manual match created successfully', 201);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to create manual match: ' . $e->getMessage());
        }
    }

    /**
     * Trigger automatic payment matching process.
     */
    public function triggerAutoMatch(): JsonResponse
    {
        try {
            $matchesCreated = $this->paymentMatchingService->executeMatching();

            // Log the admin activity
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_AUTO_MATCH_TRIGGERED,
                null,
                [
                    'matches_created' => $matchesCreated,
                    'triggered_at' => now()->toISOString(),
                ]
            );

            return $this->success([
                'matches_created' => $matchesCreated,
            ], 'Payment matching completed successfully');
        } catch (\Exception $e) {
            return $this->serverError('Failed to execute payment matching: ' . $e->getMessage());
        }
    }

    /**
     * Process payment match timeouts and deactivate users who failed to fund.
     */
    public function processTimeouts(): JsonResponse
    {
        try {
            // Get statistics before processing
            $beforeStats = $this->paymentMatchTimeoutService->getTimeoutStatistics();

            // Process timeouts
            $stats = $this->paymentMatchTimeoutService->processTimeouts();

            // Log the admin activity
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_TIMEOUT_PROCESSING_TRIGGERED,
                null,
                [
                    'before_stats' => $beforeStats,
                    'processing_stats' => $stats,
                    'triggered_at' => now()->toISOString(),
                ]
            );

            return $this->success([
                'before_stats' => $beforeStats,
                'processing_stats' => $stats,
            ], 'Payment match timeout processing completed successfully');
        } catch (\Exception $e) {
            return $this->serverError('Failed to process payment match timeouts: ' . $e->getMessage());
        }
    }

    /**
     * Get payment match timeout statistics.
     */
    public function getTimeoutStatistics(): JsonResponse
    {
        try {
            $stats = $this->paymentMatchTimeoutService->getTimeoutStatistics();

            return $this->success($stats, 'Timeout statistics retrieved successfully');
        } catch (\Exception $e) {
            return $this->serverError('Failed to get timeout statistics: ' . $e->getMessage());
        }
    }

    /**
     * Upload payment proof for a payment match.
     */
    public function uploadPaymentProof(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'payment_proof' => 'required|image|mimes:jpeg,jpg,png,gif,webp|max:5120', // 5MB max
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $match = PaymentMatch::with(['fund', 'withdraw', 'fundUser', 'withdrawUser'])->find($id);

        if (! $match) {
            return $this->notFound('Payment match not found');
        }

        // Check if the match belongs to the authenticated user (funder side)
        if ($match->fund_user_id !== auth()->id() && ! auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to upload payment proof for this match');
        }

        // Check if the match is in a valid state for payment proof upload
        if (! in_array($match->status, ['pending', 'paid'])) {
            return $this->error('Payment proof can only be uploaded for pending or paid matches', 400);
        }

        try {
            DB::beginTransaction();

            // Delete old payment proof if exists
            if ($match->payment_proof_image) {
                // Extract the filename from the URL to delete from storage
                $oldProofPath = str_replace('/storage/', '', $match->payment_proof_image);
                Storage::disk('public')->delete($oldProofPath);
            }

            // Store the new payment proof
            $file = $request->file('payment_proof');
            $filename = 'uploads/payment_proofs/match_' . $match->id . '_' . uniqid() . '_' . time() . '.' . $file->getClientOriginalExtension();
            $storedFilePath = $file->storeAs('', $filename, 'public');

            if (! $storedFilePath) {
                return $this->serverError('Failed to upload payment proof');
            }

            // Get the public URL
            $publicPath = Storage::url($storedFilePath);

            // Update the match with the payment proof image path
            $match->payment_proof_image = $publicPath;
            $match->save();

            DB::commit();

            return $this->success([
                'payment_proof_url' => $publicPath,
                'match' => $match,
            ], 'Payment proof uploaded successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to upload payment proof: ' . $e->getMessage());
        }
    }

    /**
     * Confirm payment sent for a payment match.
     *
     * For admin withdrawals:
     * - Automatically verify payment using Paystack/payment processor
     * - If verification succeeds, automatically complete both sender and receiver confirmations
     * - Update withdraw status and user stats immediately
     *
     * For user withdrawals:
     * - Mark payment as sent and wait for manual receiver confirmation
     * - Receiver must call confirmPaymentReceived() to complete the process
     */
    public function confirmPaymentSent(Request $request, string $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'transaction_hash' => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        $match = PaymentMatch::with(['fund', 'withdraw', 'fundUser', 'withdrawUser'])->find($id);

        if (! $match) {
            return $this->notFound('Payment match not found');
        }

        // Check if the match belongs to the authenticated user (funder side)
        if ($match->fund_user_id !== auth()->id() && ! auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to confirm payment for this match');
        }

        // Check if the match is in a confirmable state
        if ($match->status !== 'pending') {
            return $this->error('Only pending matches can have payments confirmed', 400);
        }

        try {
            DB::beginTransaction();

            // For admin withdrawals, verify payment based on payment method type
            if ($match->is_admin_withdraw) {
                $paymentMethod = $match->paymentMethod;
                $verificationResult = null;

                if ($paymentMethod->type === 'bank') {
                    $verificationResult = $this->paystackService->verifyPayment(
                        $request->transaction_hash,
                        [
                            'payment_match_id' => $match->id,
                            'user_id' => auth()->id(),
                            'amount' => $match->amount,
                            'currency' => $match->fund->currency ?? 'NGN',
                        ]
                    );

                    // Check if verification was successful
                    if (! $verificationResult || $verificationResult['status'] !== 'success') {
                        DB::rollBack();

                        return $this->error(
                            $verificationResult['message'] ?? 'Bank payment verification failed. Please ensure the transaction is valid.',
                            400
                        );
                    }

                    // Verify transaction was successful
                    if (! isset($verificationResult['data']['transaction_status']) ||
                        $verificationResult['data']['transaction_status'] !== 'success') {
                        DB::rollBack();

                        return $this->error('Bank payment transaction was not successful', 400);
                    }
                } elseif ($paymentMethod->type === 'crypto') {
                    // TODO: Implement actual crypto transaction verification
                    // For now, we'll use a placeholder verification that always passes
                    $cryptoVerificationPassed = true;

                    if (! $cryptoVerificationPassed) {
                        DB::rollBack();

                        return $this->error('Crypto payment verification failed. Please ensure the transaction hash is valid.', 400);
                    }

                    // Set verification result for consistency
                    $verificationResult = [
                        'status' => 'success',
                        'message' => 'Crypto payment verified successfully',
                        'data' => [
                            'transaction_status' => 'success',
                            'transaction_hash' => $request->transaction_hash,
                        ],
                    ];
                } else {
                    DB::rollBack();

                    return $this->error('Unsupported payment method type for verification', 400);
                }

                // Update the match status to paid and set confirmation fields
                $match->status = 'confirmed'; // Directly to confirmed for admin withdrawals
                $match->transaction_hash = $request->transaction_hash;
                $match->is_payment_sent_confirmed = true;
                $match->payment_sent_confirmed_at = now();
                $match->is_payment_received_confirmed = true;
                $match->payment_received_confirmed_at = now();
                $match->save();

                // Handle withdraw completion for admin withdrawals
                $withdraw = $match->withdraw;
                $allConfirmed = $withdraw->paymentMatches()->where('status', '!=', 'confirmed')->count() === 0;

                if ($allConfirmed && $withdraw->isFullyMatched()) {
                    // Update withdraw status
                    $withdraw->status = 'completed';
                    $withdraw->save();

                    // Update user stats with currency-specific tracking
                    $userStat = UserStat::where('user_id', $withdraw->user_id)->first();
                    if ($userStat) {
                        if ($withdraw->fund->currency === 'fiat') {
                            $userStat->incrementTotalFiatWithdrawn($withdraw->amount_matched);
                            $userStat->decrementPendingFiatWithdrawal($withdraw->total_withdrawable_amount);
                        } else {
                            $userStat->incrementTotalCryptoWithdrawn($withdraw->amount_matched);
                            $userStat->decrementPendingCryptoWithdrawal($withdraw->total_withdrawable_amount);
                        }
                        $userStat->save();
                    }
                }

                $message = $paymentMethod->type === 'bank'
                    ? 'Bank payment verified and confirmed automatically'
                    : 'Crypto payment verified and confirmed automatically';
            } else {
                // For user withdrawals, use the existing manual confirmation process
                $match->status = 'paid';
                $match->transaction_hash = $request->transaction_hash;
                $match->is_payment_sent_confirmed = true;
                $match->payment_sent_confirmed_at = now();
                $match->save();

                $message = 'Payment confirmed successfully. Waiting for receiver confirmation.';
            }

            // Collect total funding platform fee in the first match payment
            // Skip fee collection for admin funds (when fund owner is admin)
            if ($match->fundUser->role !== 'admin') {
                $this->collectPlatformFee($match);
            }

            // Update fund status if this was the fund's only match or if all matches are now paid/confirmed
            $fund = $match->fund;
            $allMatchesPaid = $fund->paymentMatches()->whereNotIn('status', ['paid', 'confirmed'])->count() === 0;

            if ($allMatchesPaid && $fund->status === 'matched') {
                $fund->status = 'completed';
                $fund->save();

                // Process referral bonuses now that all payments are confirmed
                // Skip referral bonuses for admin funds (when fund owner is admin)
                if ($match->fundUser->role !== 'admin') {
                    $this->processReferralBonuses($fund);
                }
            }

            DB::commit();

            return $this->success($match->fresh(), $message);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Failed to confirm payment: ' . $e->getMessage(), [
                'payment_match_id' => $id,
                'transaction_hash' => $request->transaction_hash,
                'is_admin_withdraw' => $match->is_admin_withdraw ?? false,
            ]);

            return $this->serverError('Failed to confirm payment: ' . $e->getMessage());
        }
    }

    /**
     * Confirm payment received for a payment match (withdrawer confirms receiving payment).
     */
    public function confirmPaymentReceived(Request $request, string $id): JsonResponse
    {
        $match = PaymentMatch::with(['withdraw', 'fund'])->find($id);

        if (! $match) {
            return $this->notFound('Payment match not found');
        }

        $withdraw = $match->withdraw;
        if (! $withdraw) {
            return $this->notFound('Associated withdraw not found');
        }

        // Check if the withdraw belongs to the authenticated user
        if ($withdraw->user_id !== auth()->id() && ! auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to confirm this payment');
        }

        // Check if the match is in a confirmable state
        if ($match->status !== 'paid') {
            return $this->error('Only paid matches can be confirmed as received', 400);
        }

        // Check if payment is already confirmed as received
        if ($match->is_payment_received_confirmed) {
            return $this->error('Payment reception has already been confirmed for this match', 400);
        }

        try {
            DB::beginTransaction();

            // Update match confirmation fields
            $match->is_payment_received_confirmed = true;
            $match->payment_received_confirmed_at = now();
            $match->status = 'confirmed';
            $match->save();

            // Check if all matches for this withdraw are confirmed
            $allConfirmed = $withdraw->paymentMatches()->where('status', '!=', 'confirmed')->count() === 0;

            if ($allConfirmed && $withdraw->isFullyMatched()) {
                // Update withdraw status
                $withdraw->status = 'completed';
                $withdraw->save();

                // Update user stats with currency-specific tracking
                $userStat = UserStat::where('user_id', $withdraw->user_id)->first();
                if ($userStat) {
                    if ($withdraw->fund->currency === 'fiat') {
                        $userStat->incrementTotalFiatWithdrawn($withdraw->amount_matched);
                        $userStat->decrementPendingFiatWithdrawal($withdraw->total_withdrawable_amount);
                    } else {
                        $userStat->incrementTotalCryptoWithdrawn($withdraw->amount_matched);
                        $userStat->decrementPendingCryptoWithdrawal($withdraw->total_withdrawable_amount);
                    }
                    $userStat->save();
                }
            }

            DB::commit();

            return $this->success($match, 'Payment reception confirmed successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to confirm payment reception: ' . $e->getMessage());
        }
    }

    /**
     * Process referral bonuses for a fund.
     * Only processes bonuses for completed funds with confirmed payments.
     */
    private function processReferralBonuses(Fund $fund): void
    {
        // Only process referral bonuses for completed funds with confirmed payments
        if ($fund->status !== 'completed' || ! $fund->hasConfirmedPayments()) {
            return;
        }

        $user = User::find($fund->user_id);

        if (! $user || ! $user->referrer_id) {
            return;
        }

        // Get referral percentages from settings
        $level1Percentage = PlatformSetting::getSetting('referral_level1_percentage');
        $level2Percentage = PlatformSetting::getSetting('referral_level2_percentage');
        $level3Percentage = PlatformSetting::getSetting('referral_level3_percentage');

        // Initialize user variables
        $level1User = null;
        $level2User = null;

        // Process Level 1 (direct referrer)
        if ($level1Percentage > 0) {
            $level1UserId = $user->referrer_id;
            $level1User = User::find($level1UserId);
            if (! $level1User) {
                return;
            }
            $this->createReferralBonus($fund, $level1User->id, $level1Percentage, 1);
        }

        // Process Level 2
        if ($level2Percentage > 0 && $level1User && $level1User->referrer_id) {
            $level2UserId = $level1User->referrer_id;
            $level2User = User::find($level2UserId);
            if (! $level2User) {
                return;
            }
            $this->createReferralBonus($fund, $level2User->id, $level2Percentage, 2);
        }

        // Process Level 3
        if ($level3Percentage > 0 && $level2User && $level2User->referrer_id) {
            $level3UserId = $level2User->referrer_id;
            $level3User = User::find($level3UserId);
            if (! $level3User) {
                return;
            }
            $this->createReferralBonus($fund, $level3User->id, $level3Percentage, 3);
        }
    }

    /**
     * Create a referral bonus.
     */
    private function createReferralBonus(Fund $fund, string $userId, float $percentage, int $level): void
    {
        $amount = $fund->amount * ($percentage / 100);

        ReferralBonus::create([
            'referee_user_id' => $fund->user_id,
            'referrer_user_id' => $userId,
            'fund_id' => $fund->id,
            'level' => $level,
            'percentage' => $percentage,
            'amount' => $amount,
            'is_withdrawable' => false,
            'is_consumed' => false,
        ]);

        // Update user stats
        $userStat = UserStat::firstOrCreate(['user_id' => $userId], [
            'updated_at' => now(),
        ]);
        if ($fund->currency === 'fiat') {
            $userStat->incrementAvailableFiatReferralBonus($amount);
        } else {
            $userStat->incrementAvailableCryptoReferralBonus($amount);
        }
    }

    /**
     * Collect platform fee when first payment is confirmed for a fund.
     * Creates a withdraw record for admin account to collect the fee.
     * Fee is collected only once per fund based on total fund amount.
     */
    private function collectPlatformFee(PaymentMatch $match): void
    {
        $fund = $match->fund;

        // Check if platform fee has already been collected for this fund
        if ($fund->hasPlatformFeeCollected()) {
            Log::info("Platform fee already collected for fund {$fund->id}");

            return;
        }

        // Calculate platform fee based on the total fund amount (not match amount)
        $feeAmount = $fund->calculatePlatformFeeAmount();

        // Find available admin account for platform fees
        $adminAccount = User::getAdminAccount($fund->currency);
        if (! $adminAccount) {
            Log::warning("No available admin account for platform fees: {$fund->currency}");

            return;
        }

        // Get appropriate payment method for admin account
        $adminPaymentMethod = $adminAccount->paymentMethods()->where('type', $fund->currency === 'fiat' ? 'bank' : 'crypto')->where('status', 'active')->first();

        if (! $adminPaymentMethod) {
            Log::warning("No active payment method found for admin account {$adminAccount->id} for {$fund->currency}");

            return;
        }

        try {
            DB::transaction(function () use ($fund, $feeAmount, $adminAccount, $adminPaymentMethod, $match) {
                // Double-check fee hasn't been collected (race condition protection)
                if ($fund->hasPlatformFeeCollected()) {
                    Log::info("Platform fee already collected for fund {$fund->id} (race condition avoided)");

                    return;
                }

                // Create a withdrawal record for platform fee
                $feeWithdraw = Withdraw::create([
                    'user_id' => $adminAccount->id,
                    'fund_id' => $fund->id,
                    'base_withdrawable_amount' => $feeAmount,
                    'available_referral_bonus' => 0,
                    'withdrawable_referral_bonus' => 0,
                    'total_withdrawable_amount' => $feeAmount,
                    'amount_matched' => 0,
                    'status' => 'pending',
                    'payment_method_id' => $adminPaymentMethod->id,
                ]);

                // Create platform fee record for tracking
                PlatformFee::create([
                    'fund_id' => $fund->id,
                    'admin_user_id' => $adminAccount->id,
                    'fee_withdraw_id' => $feeWithdraw->id,
                    'fund_amount' => $fund->amount,
                    'fee_amount' => $feeAmount,
                    'fee_percentage' => $fund->platform_fee_percentage,
                    'currency' => $fund->currency,
                    'collected_at' => now(),
                    'metadata' => [
                        'trigger_match_id' => $match->id,
                        'admin_payment_method_id' => $adminPaymentMethod->id,
                        'fee_method' => 'automatic_on_first_match',
                    ],
                ]);

                // Update admin account stats
                if ($adminAccount->stats) {
                    if ($fund->currency === 'fiat') {
                        $adminAccount->stats->incrementTotalFiatWithdrawn($feeAmount);
                    } else {
                        $adminAccount->stats->incrementTotalCryptoWithdrawn($feeAmount);
                    }
                    $adminAccount->stats->save();
                }

                Log::info("Platform fee collected: {$feeAmount} {$fund->currency} for fund {$fund->id} (triggered by match {$match->id})");
            });

        } catch (\Exception $e) {
            Log::error("Failed to collect platform fee for fund {$fund->id}: " . $e->getMessage());
            // Don't throw exception to avoid disrupting the main payment confirmation flow
        }
    }
}
