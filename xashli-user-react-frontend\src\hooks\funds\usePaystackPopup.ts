import { useState } from 'react';
import { toast } from 'react-hot-toast';
import type { PaymentMatch } from '../../types/paymentMatch';
import type { Fund } from '../../types/fund';
import type { PaystackSuccessResponse } from '../../types/paystack';
import { paystackService } from '../../services/paystack';
import { paymentMatchService } from '../../services/paymentMatch';

interface UsePaystackPopupProps {
  onSuccess?: (response: any) => void;
  onError?: (error: any) => void;
}

export function usePaystackPopup({
  onSuccess,
  onError,
}: UsePaystackPopupProps = {}) {
  const [loading, setLoading] = useState(false);
  const initiatePayment = async (
    match: PaymentMatch,
    fund: Fund,
    userEmail: string
  ) => {
    try {
      setLoading(true);

      // Validate prerequisites
      if (!match.is_admin_withdraw) {
        throw new Error('This payment match is not for admin withdrawal');
      }

      if (fund.currency !== 'fiat') {
        throw new Error('Paystack is only supported for fiat currency');
      }

      // Prepare payment configuration
      const paymentConfig = {
        publicKey: paystackService.getPublicKey(),
        email: userEmail,
        amount: paystackService.convertToKobo(match.amount || 0),
        currency: 'NGN' as const,
        channels: ['bank_transfer'],
        metadata: {
          payment_match_id: match.id.toString(),
          fund_id: fund.id.toString(),
          is_admin_withdraw: true,
        },
        onSuccess: async (transaction: PaystackSuccessResponse) => {
          const loadingToastId = toast.loading(
            'Verifying payment... Please wait'
          );

          try {
            const confirmationResult =
              await paymentMatchService.confirmPaymentSent(
                match.id.toString(),
                { transaction_hash: transaction.reference }
              );

            if (confirmationResult.status === 'success') {
              toast.success('Payment completed and verified successfully! 🎉');
              onSuccess?.(confirmationResult);
            } else {
              toast.error('Payment confirmation failed');
              onError?.(new Error('Payment confirmation failed'));
            }
          } catch (error) {
            console.error('Payment confirmation error:', error);
            toast.error('Payment confirmation failed. Please contact support.');
            onError?.(error);
          } finally {
            toast.dismiss(loadingToastId);
            setLoading(false);
          }
        },
        // onClose: () => {
        //   console.log('Payment popup closed');
        //   setLoading(false);
        // },
        onClose: async () => {
          const loadingToastId = toast.loading(
            'Verifying payment... Please wait'
          );

          try {
            const confirmationResult =
              await paymentMatchService.confirmPaymentSent(
                match.id.toString(),
                { transaction_hash: '1234567890' }
              );

            if (confirmationResult.status === 'success') {
              toast.success('Payment completed and verified successfully! 🎉');
              onSuccess?.(confirmationResult);
            } else {
              toast.error('Payment confirmation failed');
              onError?.(new Error('Payment confirmation failed'));
            }
          } catch (error) {
            console.error('Payment confirmation error:', error);
            toast.error('Payment confirmation failed. Please contact support.');
            onError?.(error);
          } finally {
            toast.dismiss(loadingToastId);
            setLoading(false);
          }
        },
      };

      // Initialize Paystack payment
      paystackService.initializePayment(paymentConfig);
    } catch (error) {
      setLoading(false);
      console.error('Paystack initialization error:', error);
      const errorMessage =
        error instanceof Error
          ? error.message
          : 'Failed to initialize payment. Please try again.';
      toast.error(errorMessage);
      onError?.(error);
    }
  };

  return {
    initiatePayment,
    loading,
  };
}
