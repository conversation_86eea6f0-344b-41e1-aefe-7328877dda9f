import { Link, useNavigate } from 'react-router-dom';
import { BarChart3, RefreshCw, Download, AlertCircle, Filter } from 'lucide-react';
import { Button } from '../../components/ui/Button';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { Pagination } from '../../components/ui';
import { WithdrawCard } from '../../components/withdraws/WithdrawCard';
import { WithdrawFilters } from '../../components/withdraws/WithdrawFilters';
import { DeactivatedUserAlert } from '../../components/ui/DeactivatedUserAlert';
import { useWithdraws } from '../../hooks/withdraws';
import { useAuth } from '../../contexts/AuthContext';
import { useState } from 'react';
import type { WithdrawFilters as WithdrawFiltersType } from '../../types';

export function WithdrawsPage() {
  const { user } = useAuth();
  const navigate = useNavigate();
  const {
    withdraws,
    loading,
    filters,
    pagination,
    updateFilters,
    resetFilters: resetWithdrawFilters,
    refetch,
    goToPage,
    changePageSize,
  } = useWithdraws();

  // State for showing/hiding filters and pending filters
  const [showFilters, setShowFilters] = useState(false);
  const [pendingFilters, setPendingFilters] = useState<WithdrawFiltersType>({});

  const resetFilters = () => {
    setPendingFilters({});
    resetWithdrawFilters();
  };

  const handleApplyFilters = () => {
    updateFilters(pendingFilters);
  };

  const handleClearFilters = () => {
    setPendingFilters({});
    resetWithdrawFilters();
  };

  const hasFilterChanges = () => {
    return JSON.stringify(filters) !== JSON.stringify(pendingFilters);
  };

  if (loading) {
    return (
      <div className='container mx-auto px-6 py-8'>
        <div className='flex items-center justify-center min-h-[400px]'>
          <LoadingSpinner size='lg' />
        </div>
      </div>
    );
  }

  return (
    <div className='container mx-auto px-6 py-8'>
      {/* Header */}
      <div className='flex flex-col sm:flex-row sm:items-center justify-between gap-4 mb-8'>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>My Withdraws</h1>
          <p className='text-foreground-secondary mt-2'>
            Manage your withdrawal requests and track payment matches
          </p>
        </div>
        <div className='flex gap-2'>
          <Button
            variant='outline'
            onClick={() => setShowFilters(!showFilters)}
            className={`flex items-center gap-2 ${
              hasFilterChanges()
                ? 'bg-orange-900/20 border-orange-500/30 text-orange-300 hover:bg-orange-900/30 hover:border-orange-500/50'
                : 'bg-blue-900/20 border-blue-500/30 text-blue-300 hover:bg-blue-900/30 hover:border-blue-500/50'
            }`}
          >
            <Filter className='h-4 w-4' />
            {showFilters ? 'Hide Filters' : 'Show Filters'}
            {hasFilterChanges() && <span className='ml-1 text-xs'>(*)</span>}
          </Button>
          <Button
            variant='outline'
            className='flex items-center gap-2'
            onClick={() => refetch()}
            disabled={loading}
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
          <Link to='/withdraws/statistics' className='shrink-0'>
            <Button variant='outline' className='flex items-center gap-2'>
              <BarChart3 className='h-4 w-4' />
              Statistics
            </Button>
          </Link>
        </div>
      </div>

      {/* Deactivated User Alert */}
      {user && !user.is_active && (
        <div className='mb-6'>
          <DeactivatedUserAlert
            user={user}
            customMessage='Your account has been deactivated. You cannot create new withdrawal requests while your account is inactive.'
          />
        </div>
      )}

      {/* Phone Number Reminder for Active Users */}
      {user && user.is_active && (
        <div className='mb-6'>
          <div className='bg-blue-950/20 border border-blue-800/30 rounded-lg p-4'>
            <div className='flex items-start gap-3'>
              <AlertCircle className='h-5 w-5 text-blue-400 flex-shrink-0 mt-0.5' />
              <div>
                <h3 className='font-medium text-blue-200 mb-1'>
                  Keep Your Phone Active
                </h3>
                <p className='text-sm text-blue-300'>
                  Ensure your registered phone number is active and reachable so
                  funders can contact you during payment processes. This helps
                  avoid payment delays and potential account issues.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Filters */}
      {showFilters && (
        <div className='mb-6'>
          <WithdrawFilters
            filters={pendingFilters}
            onFiltersChange={setPendingFilters}
            onReset={handleClearFilters}
            onApply={handleApplyFilters}
            hasChanges={hasFilterChanges()}
          />
        </div>
      )}

      {/* Withdraws Grid */}
      {!Array.isArray(withdraws) || withdraws.length === 0 ? (
        <div className='text-center py-12'>
          <div className='mx-auto w-24 h-24 bg-background-secondary rounded-full flex items-center justify-center mb-4'>
            <Download className='h-12 w-12 text-foreground-muted' />
          </div>
          <h3 className='text-xl font-semibold text-foreground mb-2'>
            No Withdraws Found
          </h3>
          <p className='text-foreground-secondary mb-6 max-w-md mx-auto'>
            {Object.keys(filters).length > 0
              ? 'No withdraws match your current filters. Try adjusting your search criteria.'
              : 'You have not made any withdrawal requests yet. Withdraws will appear here once you request them from your matured funds.'}
          </p>
          {Object.keys(filters).length > 0 ? (
            <Button onClick={resetFilters} variant='outline'>
              Clear Filters
            </Button>
          ) : (
            <Link to='/funds'>
              <Button>
                <AlertCircle className='h-4 w-4 mr-2' />
                View My Funds
              </Button>
            </Link>
          )}
        </div>
      ) : (
        <>
          <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
            {withdraws.map(withdraw => (
              <WithdrawCard
                key={withdraw.id}
                withdraw={withdraw}
                onView={() => navigate(`/withdraws/${withdraw.id}`)}
              />
            ))}
          </div>

          {/* Pagination */}
          {pagination.total > 0 && (
            <div className='mt-8'>
              <Pagination
                pagination={pagination}
                currentPage={pagination.current_page}
                pageSize={pagination.per_page}
                onPageChange={goToPage}
                onPageSizeChange={changePageSize}
                isLoading={loading}
              />
            </div>
          )}
        </>
      )}
    </div>
  );
}
