import { Filter, RotateCcw } from 'lucide-react';
import { Button } from '../ui/Button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/Select';
import type { FundFilters as FundFiltersType } from '../../types';

interface FundFiltersProps {
  filters: FundFiltersType;
  onFiltersChange: (filters: FundFiltersType) => void;
  onReset: () => void;
}

export function FundFilters({
  filters,
  onFiltersChange,
  onReset,
}: FundFiltersProps) {
  const handleFilterChange = (key: keyof FundFiltersType, value: string) => {
    const newFilters = { ...filters };
    if (value === 'all' || value === '') {
      delete newFilters[key];
    } else {
      newFilters[key] = value as any;
    }
    onFiltersChange(newFilters);
  };

  const hasActiveFilters = Object.keys(filters).length > 0;
  return (
    <div className='bg-background rounded-lg border border-border p-4'>
      <div className='flex items-center gap-2 mb-4'>
        <Filter className='h-4 w-4 text-foreground-secondary' />
        <h3 className='font-medium text-foreground'>Filters</h3>
        {hasActiveFilters && (
          <Button
            variant='ghost'
            size='sm'
            onClick={onReset}
            className='ml-auto text-xs'
          >
            <RotateCcw className='h-3 w-3 mr-1' />
            Reset
          </Button>
        )}
      </div>

      <div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4'>
        {' '}
        {/* Status Filter */}
        <div>
          <label className='block text-sm font-medium text-foreground-secondary mb-1'>
            Status
          </label>
          <Select
            value={filters.status || 'all'}
            onValueChange={value => handleFilterChange('status', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder='All Statuses' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Statuses</SelectItem>
              <SelectItem value='pending'>Pending</SelectItem>
              <SelectItem value='matched'>Matched</SelectItem>
              <SelectItem value='completed'>Completed</SelectItem>
              <SelectItem value='cancelled'>Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {/* Currency Filter */}
        <div>
          <label className='block text-sm font-medium text-foreground-secondary mb-1'>
            Currency
          </label>
          <Select
            value={filters.currency || 'all'}
            onValueChange={value => handleFilterChange('currency', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder='All Currencies' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='all'>All Currencies</SelectItem>
              <SelectItem value='fiat'>Fiat (NGN)</SelectItem>
              <SelectItem value='crypto'>Crypto (SOL)</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {/* Sort Field */}
        <div>
          <label className='block text-sm font-medium text-foreground-secondary mb-1'>
            Sort By
          </label>
          <Select
            value={filters.sort_field || 'created_at'}
            onValueChange={value => handleFilterChange('sort_field', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder='Date Created' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='created_at'>Date Created</SelectItem>
              <SelectItem value='amount'>Amount</SelectItem>
              <SelectItem value='status'>Status</SelectItem>
              <SelectItem value='currency'>Currency</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {/* Sort Direction */}
        <div>
          <label className='block text-sm font-medium text-foreground-secondary mb-1'>
            Sort Order
          </label>
          <Select
            value={filters.sort_direction || 'desc'}
            onValueChange={value => handleFilterChange('sort_direction', value)}
          >
            <SelectTrigger>
              <SelectValue placeholder='Newest First' />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value='desc'>Newest First</SelectItem>
              <SelectItem value='asc'>Oldest First</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>
    </div>
  );
}
