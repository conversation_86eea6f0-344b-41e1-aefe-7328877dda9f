import React, { useEffect, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  ArrowLeft,
  CreditCard,
  Wallet,
  AlertCircle,
  Info,
  CheckCircle,
} from 'lucide-react';
import { Button } from '../../components/ui/Button';
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  Input,
  Label,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui';
import { LoadingSpinner } from '../../components/ui/LoadingSpinner';
import { ConfirmationCodeSection } from '../../components/ui/ConfirmationCodeSection';
import {
  usePaymentMethodForm,
  useBanks,
  useAccountValidation,
} from '../../hooks/payment-methods';
import { useConfirmationCode } from '../../hooks/useConfirmationCode';

export function CreatePaymentMethodPage() {
  const navigate = useNavigate();

  const {
    type,
    setType,
    formData,
    errors,
    loading,
    handleInputChange,
    updateFormData,
    clearError,
    validateForm,
    handleSubmit,
  } = usePaymentMethodForm();

  const {
    banks,
    bankSearchTerm,
    setBankSearchTerm,
    loadingBanks,
    filteredBanks,
    loadBanks,
    getBankByCode,
    clearBankSearch,
  } = useBanks();

  const {
    validatingAccount,
    accountValidated,
    validateAccount,
    resetValidation,
  } = useAccountValidation();

  // Confirmation code hook
  const confirmationCode = useConfirmationCode({
    action: 'create',
    context: 'payment_method',
  });

  // Track the last validated account number to prevent unnecessary API calls
  const [lastValidatedAccountNumber, setLastValidatedAccountNumber] =
    useState<string>('');

  // Auto-validate account when 10 digits are entered
  const handleAutoValidation = useCallback(
    async (accountNumber: string, bankCode: string) => {
      if (
        accountNumber.length === 10 &&
        bankCode &&
        accountNumber !== lastValidatedAccountNumber
      ) {
        setLastValidatedAccountNumber(accountNumber);
        await validateAccount(
          bankCode,
          accountNumber,
          accountName => {
            updateFormData({ account_name: accountName });
            clearError('account_number');
            clearError('account_name');
          },
          () => {
            // Error handling is already done in the hook
          }
        );
      }
    },
    [lastValidatedAccountNumber, validateAccount, updateFormData, clearError]
  );

  useEffect(() => {
    if (type === 'bank') {
      loadBanks();
    }
    // Clear bank search when switching types
    clearBankSearch();
  }, [type]);

  const handleFormInputChange = (field: string, value: string) => {
    handleInputChange(field, value);

    // Set bank name when bank code is selected
    if (field === 'bank_code') {
      const selectedBank = getBankByCode(value);
      if (selectedBank) {
        updateFormData({ bank_name: selectedBank.name });
      }
    }

    // Clear validation when changing bank or account number
    if (field === 'bank_code' || field === 'account_number') {
      resetValidation();
      updateFormData({ account_name: '' });
      setLastValidatedAccountNumber('');
    }

    // Auto-validate account when 10 digits are entered
    if (
      field === 'account_number' &&
      value.length === 10 &&
      formData.bank_code
    ) {
      handleAutoValidation(value, formData.bank_code as string);
    }

    // Clear bank search when a bank is selected
    if (field === 'bank_code') {
      clearBankSearch();
    }
  };

  const onSubmit = (e: React.FormEvent) => {
    // Add confirmation code to form data before submission
    updateFormData({ confirmation_code: confirmationCode.code });

    handleSubmit(
      e,
      () => validateForm(accountValidated),
      () => navigate('/payment-methods')
    );
  };

  return (
    <div className='container mx-auto px-6 py-8 max-w-2xl'>
      {/* Header */}
      <div className='flex items-center gap-4 mb-8'>
        <Button
          variant='ghost'
          size='sm'
          onClick={() => navigate('/payment-methods')}
          className='p-2'
        >
          <ArrowLeft className='h-4 w-4' />
        </Button>
        <div>
          <h1 className='text-3xl font-bold text-foreground'>
            Add Payment Method
          </h1>
          <p className='text-foreground-secondary mt-2'>
            Add a new bank account or crypto wallet
          </p>
        </div>
      </div>

      <form onSubmit={onSubmit} className='space-y-6'>
        {/* Information Section */}
        <Card className='border-primary/20 bg-primary/5'>
          <CardContent className='p-4'>
            <div className='flex items-center gap-3'>
              <Info className='h-5 w-5 text-primary flex-shrink-0' />
              <p className='text-sm text-foreground'>
                This payment method will be used to send you funds when you're
                matched with someone.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Payment Type Selection */}
        <Card>
          <CardHeader className='pb-4'>
            <CardTitle>Payment Method Type</CardTitle>
          </CardHeader>
          <CardContent className='pt-0 space-y-4'>
            <div className='grid grid-cols-2 gap-4'>
              <button
                type='button'
                onClick={() => setType('bank')}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  type === 'bank'
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-border-hover'
                }`}
              >
                <div className='flex flex-col items-center gap-2'>
                  <CreditCard className='h-8 w-8 text-primary' />
                  <span className='font-medium'>Bank Account</span>
                </div>
              </button>

              <button
                type='button'
                onClick={() => setType('crypto')}
                className={`p-4 rounded-lg border-2 transition-colors ${
                  type === 'crypto'
                    ? 'border-primary bg-primary/5'
                    : 'border-border hover:border-border-hover'
                }`}
              >
                <div className='flex flex-col items-center gap-2'>
                  <Wallet className='h-8 w-8 text-primary' />
                  <span className='font-medium'>Crypto Wallet</span>
                </div>
              </button>
            </div>
          </CardContent>
        </Card>

        {/* Payment Method Details */}
        <Card>
          <CardHeader className='pb-4'>
            <CardTitle>
              {type === 'bank'
                ? 'Bank Account Details'
                : 'Crypto Wallet Details'}
            </CardTitle>
          </CardHeader>
          <CardContent className='pt-0 space-y-4'>
            {type === 'bank' ? (
              <>
                {/* Bank Selection */}
                <div className='space-y-2'>
                  <Label>Bank *</Label>
                  <Select
                    value={formData.bank_code || ''}
                    onValueChange={(value: string) =>
                      handleFormInputChange('bank_code', value)
                    }
                  >
                    <SelectTrigger
                      className={errors.bank_code ? 'border-destructive' : ''}
                    >
                      {formData.bank_code ? (
                        <span>
                          {banks.find(bank => bank.code === formData.bank_code)
                            ?.name || 'Select your bank'}
                        </span>
                      ) : (
                        <SelectValue placeholder='Select your bank' />
                      )}
                    </SelectTrigger>
                    <SelectContent>
                      {loadingBanks ? (
                        <div className='flex items-center justify-center p-4'>
                          <LoadingSpinner size='sm' />
                        </div>
                      ) : (
                        <>
                          {/* Search Input */}
                          <div className='p-2 border-b border-border'>
                            <Input
                              placeholder='Search banks...'
                              value={bankSearchTerm}
                              onChange={e => setBankSearchTerm(e.target.value)}
                              onClick={e => e.stopPropagation()}
                              onKeyDown={e => e.stopPropagation()}
                              className='h-8 text-sm'
                            />
                          </div>
                          {/* Bank Options */}
                          <div className='max-h-48 overflow-auto'>
                            {filteredBanks.length === 0 ? (
                              <div className='p-2 text-sm text-foreground-secondary text-center'>
                                No banks found
                              </div>
                            ) : (
                              filteredBanks.map(bank => (
                                <SelectItem key={bank.code} value={bank.code}>
                                  {bank.name}
                                </SelectItem>
                              ))
                            )}
                          </div>
                        </>
                      )}
                    </SelectContent>
                  </Select>
                  {errors.bank_code && (
                    <p className='text-sm text-destructive flex items-center gap-1'>
                      <AlertCircle className='h-3 w-3' />
                      {errors.bank_code}
                    </p>
                  )}
                </div>

                {/* Account Number */}
                <div className='space-y-2'>
                  <Label htmlFor='account_number'>Account Number *</Label>
                  <Input
                    id='account_number'
                    placeholder='Enter your 10-digit account number'
                    value={formData.account_number || ''}
                    onChange={e =>
                      handleFormInputChange('account_number', e.target.value)
                    }
                    className={
                      errors.account_number ? 'border-destructive' : ''
                    }
                    maxLength={10}
                  />
                  {/* Verification status indicator */}
                  {formData.account_number &&
                    formData.account_number.length === 10 && (
                      <div className='flex items-center gap-1 text-xs'>
                        {validatingAccount ? (
                          <>
                            <LoadingSpinner
                              size='sm'
                              className='h-3 w-3 text-amber-500'
                            />
                            <span className='text-amber-600 font-medium'>
                              Verifying account...
                            </span>
                          </>
                        ) : accountValidated ? (
                          <>
                            <CheckCircle className='h-3 w-3 text-green-600' />
                            <span className='text-green-600'>
                              Account verified
                            </span>
                          </>
                        ) : (
                          <span className='text-foreground-secondary'>
                            Checking account...
                          </span>
                        )}
                      </div>
                    )}
                  {errors.account_number && (
                    <p className='text-sm text-destructive flex items-center gap-1'>
                      <AlertCircle className='h-3 w-3' />
                      {errors.account_number}
                    </p>
                  )}
                </div>

                {/* Account Name */}
                <div className='space-y-2'>
                  <Label htmlFor='account_name'>Account Name *</Label>
                  <Input
                    id='account_name'
                    placeholder='Account holder name'
                    value={formData.account_name || ''}
                    onChange={e =>
                      handleFormInputChange('account_name', e.target.value)
                    }
                    className={errors.account_name ? 'border-destructive' : ''}
                    readOnly={accountValidated}
                  />
                  {errors.account_name && (
                    <p className='text-sm text-destructive flex items-center gap-1'>
                      <AlertCircle className='h-3 w-3' />
                      {errors.account_name}
                    </p>
                  )}
                </div>
              </>
            ) : (
              <>
                {/* Crypto Network */}
                <div className='space-y-2'>
                  <Label>Crypto Network *</Label>
                  <Select
                    value={formData.crypto_network || ''}
                    onValueChange={(value: string) =>
                      handleFormInputChange('crypto_network', value)
                    }
                  >
                    <SelectTrigger
                      className={
                        errors.crypto_network ? 'border-destructive' : ''
                      }
                    >
                      <SelectValue placeholder='Select crypto network' />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value='Solana'>Solana (SOL)</SelectItem>
                    </SelectContent>
                  </Select>
                  {errors.crypto_network && (
                    <p className='text-sm text-destructive flex items-center gap-1'>
                      <AlertCircle className='h-3 w-3' />
                      {errors.crypto_network}
                    </p>
                  )}
                </div>

                {/* Wallet Address */}
                <div className='space-y-2'>
                  <Label htmlFor='wallet_address'>Wallet Address *</Label>
                  <Input
                    id='wallet_address'
                    placeholder='Enter your Solana wallet address'
                    value={formData.wallet_address || ''}
                    onChange={e =>
                      handleFormInputChange('wallet_address', e.target.value)
                    }
                    className={`font-mono ${errors.wallet_address ? 'border-destructive' : ''}`}
                  />
                  {errors.wallet_address && (
                    <p className='text-sm text-destructive flex items-center gap-1'>
                      <AlertCircle className='h-3 w-3' />
                      {errors.wallet_address}
                    </p>
                  )}
                </div>
              </>
            )}
          </CardContent>
        </Card>

        {/* Confirmation Code Section */}
        <ConfirmationCodeSection
          code={confirmationCode.code}
          onCodeChange={confirmationCode.setCode}
          onRequestCode={confirmationCode.requestCode}
          isRequesting={confirmationCode.isRequesting}
          isRequested={confirmationCode.isRequested}
          canRequest={confirmationCode.canRequest}
          cooldownRemaining={confirmationCode.cooldownRemaining}
          error={confirmationCode.error}
          title='Security Confirmation Required'
          description='For your security, please request and enter a confirmation code to create this payment method.'
        />

        {/* Form Actions */}
        <div className='flex items-center justify-end gap-4 pt-6 border-t'>
          <Button
            type='button'
            variant='outline'
            onClick={() => navigate('/payment-methods')}
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type='submit'
            disabled={loading || !confirmationCode.isCodeValid}
          >
            {loading ? (
              <>
                <LoadingSpinner size='sm' className='mr-2' />
                Creating...
              </>
            ) : (
              'Create Payment Method'
            )}
          </Button>
        </div>
      </form>
    </div>
  );
}
