<?php

namespace App\Http\Controllers;

use App\Models\Fund;
use App\Models\ReferralBonus;
use App\Models\UserStat;
use App\Models\Withdraw;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class WithdrawController extends Controller
{
    /**
     * Display a listing of withdraws.
     * Admins can view all withdraws, users can only view their own.
     */
    public function index(Request $request): JsonResponse
    {
        // Admin can view all withdraws, users only their own
        if (auth()->user()->isAdmin()) {
            $query = Withdraw::with(['user', 'fund', 'fund.nextFund', 'paymentMethod']);

            // Filter by user_id if provided (admin feature)
            if ($request->has('user_id') && ! empty($request->user_id)) {
                $query->where('user_id', $request->user_id);
            }

            // Filter by user email if provided (admin-specific filter)
            if ($request->has('email') && ! empty($request->email)) {
                $query->whereHas('user', function ($q) use ($request) {
                    $q->where('email', 'like', '%' . $request->email . '%');
                });
            }
        } else {
            $query = auth()->user()->withdraws()->with(['fund', 'fund.nextFund', 'paymentMethod']);
        }

        // Filter by status if provided
        if ($request->has('status') && in_array($request->status, ['pending', 'matched', 'completed'])) {
            $query->where('status', $request->status);
        }

        // Filter by currency if provided
        if ($request->has('currency') && in_array($request->currency, ['fiat', 'crypto'])) {
            $query->whereHas('fund', function ($q) use ($request) {
                $q->where('currency', $request->currency);
            });
        }

        // Search by amount or transaction details
        if ($request->has('search') && ! empty($request->search)) {
            $searchTerm = $request->search;
            $query->where(function ($q) use ($searchTerm) {
                $q->where('base_withdrawable_amount', 'like', "%{$searchTerm}%")
                    ->orWhere('amount_matched', 'like', "%{$searchTerm}%")
                    ->orWhereHas('fund', function ($fundQuery) use ($searchTerm) {
                        $fundQuery->where('amount', 'like', "%{$searchTerm}%");
                    });
            });
        }

        // Date range filter
        if ($request->has('start_date')) {
            $query->whereDate('created_at', '>=', $request->start_date);
        }
        if ($request->has('end_date')) {
            $query->whereDate('created_at', '<=', $request->end_date);
        }

        // Sorting
        $sortField = $request->get('sort_field', 'created_at');
        $sortDirection = $request->get('sort_direction', 'desc');

        // Whitelist allowed sort fields for security
        $allowedSortFields = ['base_withdrawable_amount', 'amount_matched', 'created_at', 'status'];
        if (! in_array($sortField, $allowedSortFields)) {
            $sortField = 'created_at';
        }

        // Validate sort direction
        if (! in_array($sortDirection, ['asc', 'desc'])) {
            $sortDirection = 'desc';
        }

        // Pagination
        $isAdmin = auth()->user()->isAdmin();
        $perPage = min($request->get('per_page', 15), $isAdmin ? 100 : 50);
        $paginatedWithdraws = $query->orderBy($sortField, $sortDirection)->paginate($perPage);

        // Structure response with separate withdraws and pagination
        $response = [
            'withdraws' => $paginatedWithdraws->items(),
            'pagination' => [
                'current_page' => $paginatedWithdraws->currentPage(),
                'last_page' => $paginatedWithdraws->lastPage(),
                'per_page' => $paginatedWithdraws->perPage(),
                'total' => $paginatedWithdraws->total(),
                'from' => $paginatedWithdraws->firstItem(),
                'to' => $paginatedWithdraws->lastItem(),
            ],
        ];

        return $this->success($response, 'Withdraws retrieved successfully');
    }

    /**
     * Store a newly created withdraw in storage.
     */
    public function store(Request $request): JsonResponse
    {
        // Check user account status first, before any other validation
        $user = auth()->user();

        // Auto-reactivate if deactivation period has expired
        $user->autoReactivateIfExpired();

        if (! $user->is_active) {
            return $this->forbidden('Your account has been deactivated. Please contact support or wait for automatic reactivation.');
        }

        $validator = Validator::make($request->all(), [
            'fund_id' => 'required|uuid|exists:funds,id',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        // Check if fund belongs to the user
        $fund = Fund::find($request->fund_id);
        if (! $fund || $fund->user_id !== auth()->id()) {
            return $this->error('Invalid fund', 400);
        }

        // Check if all payment matches are settled
        if (! $fund->arePaymentMatchesSettled()) {
            return $this->error('You must confirm all funding matches before withdrawing', 400);
        }

        // Check if fund is matured for withdrawal
        if (! $fund->isMatured()) {
            return $this->error('Fund is not yet matured', 400);
        }

        // Check if the fund owner has already initiated a withdrawal for this fund
        if ($fund->withdraw_id !== null) {
            return $this->error('Fund has already been withdrawn', 400);
        }

        // Get payment method from the fund
        $paymentMethod = $fund->paymentMethod;
        if (! $paymentMethod) {
            return $this->error('No payment method associated with this fund', 400);
        }

        // Check if the fund has a linked next fund
        if (! $fund->next_fund_id) {
            return $this->error('No linked fund found for withdrawal. Please create a new fund first.', 400);
        }

        $nextFund = Fund::find($fund->next_fund_id);
        if (! $nextFund || $nextFund->user_id !== auth()->id()) {
            return $this->error('Invalid linked fund', 400);
        }

        // Check if new fund amount is greater than or equal to the original fund amount
        if ($nextFund->amount < $fund->amount) {
            return $this->error('New fund amount must be greater than or equal to the original fund amount', 400);
        }

        // Check if payment method type matches the fund currency
        if (($fund->currency === 'fiat' && $paymentMethod->type !== 'bank') ||
            ($fund->currency === 'crypto' && $paymentMethod->type !== 'crypto')) {
            return $this->error('Payment method type does not match the fund currency', 400);
        }

        try {
            DB::beginTransaction();

            // Calculate eligible amount (original amount + growth)
            $baseWithdrawableAmount = $fund->amount + $fund->growth_amount;

            // Get user stats for referral bonus tracking
            $userStat = UserStat::firstOrCreate(['user_id' => auth()->id()], [
                'updated_at' => now(),
            ]);

            // Get currency-specific available referral bonus from user stats
            $availableReferralBonus = $fund->currency === 'fiat'
                ? $userStat->getAvailableFiatReferralBonus()
                : $userStat->getAvailableCryptoReferralBonus();

            // Apply bonus withdrawal limitation
            // The referral_bonus_limit is already calculated to ensure that
            // referral bonus + growth amount doesn't exceed 100% of fund amount
            $maxReferralBonus = $fund->referral_bonus_limit;
            $withdrawableReferralBonus = min($availableReferralBonus, $maxReferralBonus);

            // Calculate total withdrawable amount
            $totalWithdrawableAmount = $baseWithdrawableAmount + $withdrawableReferralBonus;

            // Double-check that total withdrawal doesn't exceed fund amount
            if ($totalWithdrawableAmount > $fund->amount * 2) {
                DB::rollBack();

                return $this->error('Total withdrawal amount exceeds maximum allowed limit', 400);
            }

            // Create the withdraw
            $withdraw = Withdraw::create([
                'user_id' => auth()->id(),
                'fund_id' => $fund->id,
                'base_withdrawable_amount' => $baseWithdrawableAmount,
                'available_referral_bonus' => $availableReferralBonus,
                'withdrawable_referral_bonus' => $withdrawableReferralBonus,
                'total_withdrawable_amount' => $totalWithdrawableAmount,
                'amount_matched' => 0,
                'status' => 'pending',
                'payment_method_id' => $paymentMethod->id,
            ]);

            // Ensure the relationship is properly set in both directions
            if ($nextFund->prev_fund_id !== $fund->id) {
                $nextFund->prev_fund_id = $fund->id;
                $nextFund->save();
            }

            // Mark the fund as eligible for withdrawal and set withdraw_id
            $fund->is_eligible_for_withdrawal = true;
            $fund->withdraw_id = $withdraw->id;
            $fund->save();

            // Consume referral bonus from user stats
            if ($withdrawableReferralBonus > 0) {
                if ($fund->currency === 'fiat') {
                    $userStat->consumeFiatReferralBonus($withdrawableReferralBonus);
                } else {
                    $userStat->consumeCryptoReferralBonus($withdrawableReferralBonus);
                }

                // Create a record of the consumed bonus for historical purposes
                ReferralBonus::create([
                    'referee_user_id' => $fund->user_id,
                    'referrer_user_id' => auth()->id(),
                    'fund_id' => $fund->id,
                    'level' => 0, // Special level for withdrawal usage
                    'percentage' => 0, // Not applicable for withdrawal usage
                    'amount' => $withdrawableReferralBonus,
                    'is_withdrawable' => false, // Already consumed
                    'is_consumed' => true,
                ]);
            }

            // Update user stats
            if ($fund->currency === 'fiat') {
                $userStat->incrementPendingFiatWithdrawal($baseWithdrawableAmount + $withdrawableReferralBonus);
            } else {
                $userStat->incrementPendingCryptoWithdrawal($baseWithdrawableAmount + $withdrawableReferralBonus);
            }

            DB::commit();

            return $this->success($withdraw, 'Withdraw created successfully', 201);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to create withdraw: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified withdraw.
     */
    public function show(string $id): JsonResponse
    {
        $withdraw = Withdraw::with([
            'user:id,full_name,email,phone',
            'fund',
            'fund.nextFund',
            'paymentMethod',
            'paymentMatches.fund',
            'paymentMatches.paymentMethod',
            'paymentMatches.dispute',
        ])->find($id);

        if (! $withdraw) {
            return $this->notFound('Withdraw not found');
        }

        // Check if the withdraw belongs to the authenticated user
        if ($withdraw->user_id !== auth()->id() && ! auth()->user()->isAdmin()) {
            return $this->forbidden('You do not have permission to view this withdraw');
        }

        return $this->success($withdraw->setAttribute('total_to_fully_match', $withdraw->total_to_fully_match), 'Withdraw retrieved successfully');
    }

    /**
     * Get withdraw statistics.
     * Admins can view platform-wide statistics, users view their own statistics.
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $user = auth()->user();
            $isAdmin = $user->isAdmin();

            // Build base query depending on user role
            if ($isAdmin) {
                $query = Withdraw::with(['fund']);

                // Allow admin to filter by user_id if provided
                if ($request->has('user_id') && ! empty($request->user_id)) {
                    $query->where('user_id', $request->user_id);
                }

                // Allow admin to filter by user email if provided
                if ($request->has('email') && ! empty($request->email)) {
                    $query->whereHas('user', function ($q) use ($request) {
                        $q->where('email', 'like', '%' . $request->email . '%');
                    });
                }
            } else {
                $query = $user->withdraws()->with(['fund']);
            }

            // Optional date range filtering
            if ($request->has('date_from') && ! empty($request->date_from)) {
                $query->whereDate('created_at', '>=', $request->date_from);
            }

            if ($request->has('date_to') && ! empty($request->date_to)) {
                $query->whereDate('created_at', '<=', $request->date_to);
            }

            $withdraws = $query->get();

            // Group withdraws by status for efficient calculations
            $withdrawsByStatus = $withdraws->groupBy('status');

            // Initialize status groups to ensure all statuses are represented
            $statuses = ['pending', 'matched', 'completed'];
            foreach ($statuses as $status) {
                if (! $withdrawsByStatus->has($status)) {
                    $withdrawsByStatus[$status] = collect();
                }
            }

            // Helper function to calculate status stats with withdraw-specific amount logic
            $calculateStatusStats = function ($statusWithdraws, $status) {
                $withdrawsWithFund = $statusWithdraws->filter(fn ($w) => $w->fund);

                $fiatWithdraws = $withdrawsWithFund->filter(fn ($w) => $w->fund->currency === 'fiat');
                $cryptoWithdraws = $withdrawsWithFund->filter(fn ($w) => $w->fund->currency === 'crypto');

                // Use appropriate amount based on status
                $getFiatAmount = function ($withdraws, $status) {
                    return $withdraws->sum(fn ($w) => $status === 'pending'
                        ? $w->total_withdrawable_amount
                        : $w->amount_matched
                    );
                };

                $getCryptoAmount = function ($withdraws, $status) {
                    return $withdraws->sum(fn ($w) => $status === 'pending'
                        ? $w->total_withdrawable_amount
                        : $w->amount_matched
                    );
                };

                return [
                    'total_count' => $statusWithdraws->count(),
                    'count' => [
                        'fiat' => $fiatWithdraws->count(),
                        'crypto' => $cryptoWithdraws->count(),
                    ],
                    'amount' => [
                        'fiat' => round($getFiatAmount($fiatWithdraws, $status), 2),
                        'crypto' => round($getCryptoAmount($cryptoWithdraws, $status), 6),
                    ],
                ];
            };

            // Calculate overall statistics with mixed status logic
            $withdrawsWithFund = $withdraws->filter(fn ($w) => $w->fund);
            $fiatWithdraws = $withdrawsWithFund->filter(fn ($w) => $w->fund->currency === 'fiat');
            $cryptoWithdraws = $withdrawsWithFund->filter(fn ($w) => $w->fund->currency === 'crypto');

            $getTotalFiatAmount = function ($withdraws) {
                return $withdraws->sum(fn ($w) => $w->status === 'pending'
                    ? $w->total_withdrawable_amount
                    : $w->amount_matched
                );
            };

            $getTotalCryptoAmount = function ($withdraws) {
                return $withdraws->sum(fn ($w) => $w->status === 'pending'
                    ? $w->total_withdrawable_amount
                    : $w->amount_matched
                );
            };

            $overallStats = [
                'total_count' => $withdrawsWithFund->count(),
                'count' => [
                    'fiat' => $fiatWithdraws->count(),
                    'crypto' => $cryptoWithdraws->count(),
                ],
                'amount' => [
                    'fiat' => round($getTotalFiatAmount($fiatWithdraws), 2),
                    'crypto' => round($getTotalCryptoAmount($cryptoWithdraws), 6),
                ],
            ];

            $statistics = [
                'overview' => [
                    'total_count' => $overallStats['total_count'],
                    'count' => $overallStats['count'],
                    'amount' => $overallStats['amount'],
                ],
                'statuses' => [
                    'pending' => $calculateStatusStats($withdrawsByStatus['pending'], 'pending'),
                    'matched' => $calculateStatusStats($withdrawsByStatus['matched'], 'matched'),
                    'completed' => $calculateStatusStats($withdrawsByStatus['completed'], 'completed'),
                ],
            ];

            return $this->success($statistics, 'Withdraw statistics retrieved successfully');
        } catch (\Exception $e) {
            return $this->serverError('Failed to retrieve withdraw statistics: ' . $e->getMessage());
        }
    }
}
