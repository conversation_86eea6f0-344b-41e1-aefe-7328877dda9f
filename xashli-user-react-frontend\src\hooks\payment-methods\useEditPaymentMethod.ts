import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import toast from 'react-hot-toast';
import { paymentMethodService } from '../../services';
import type { PaymentMethod, UpdatePaymentMethodRequest } from '../../types';

export function useEditPaymentMethod(id?: string) {
  const navigate = useNavigate();

  const [paymentMethod, setPaymentMethod] = useState<PaymentMethod | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [accountValidated, setAccountValidated] = useState(true);

  const [formData, setFormData] = useState<Partial<UpdatePaymentMethodRequest>>(
    {
      bank_name: '',
      bank_code: '',
      account_number: '',
      account_name: '',
      crypto_network: '',
      wallet_address: '',
    }
  );

  const [errors, setErrors] = useState<Record<string, string>>({});

  useEffect(() => {
    if (id) {
      loadPaymentMethod();
    }
  }, [id]);

  const loadPaymentMethod = async () => {
    if (!id) {
      navigate('/payment-methods');
      return;
    }

    try {
      setLoading(true);
      const response = await paymentMethodService.getPaymentMethod(id);

      if (response.status === 'success' && response.data) {
        const pm = response.data;
        setPaymentMethod(pm);
        setFormData({
          bank_name: pm.bank_name || '',
          bank_code: pm.bank_code || '',
          account_number: pm.account_number || '',
          account_name: pm.account_name || '',
          crypto_network: pm.crypto_network || '',
          wallet_address: pm.wallet_address || '',
        });
      } else {
        toast.error('Payment method not found');
        navigate('/payment-methods');
      }
    } catch (error) {
      console.error('Error loading payment method:', error);
      toast.error('Failed to load payment method');
      navigate('/payment-methods');
    } finally {
      setLoading(false);
    }
  };
  const handleInputChange = (field: string, value: string) => {
    setFormData((prev: Partial<UpdatePaymentMethodRequest>) => ({
      ...prev,
      [field]: value,
    }));

    // Check if bank or account details changed to require re-validation
    if (
      paymentMethod?.type === 'bank' &&
      (field === 'bank_code' || field === 'account_number')
    ) {
      const bankChanged =
        field === 'bank_code' && value !== paymentMethod.bank_code;
      const accountChanged =
        field === 'account_number' && value !== paymentMethod.account_number;

      if (bankChanged || accountChanged) {
        setAccountValidated(false);
        if (field === 'bank_code') {
          setFormData((prev: Partial<UpdatePaymentMethodRequest>) => ({
            ...prev,
            account_name: '',
          }));
        }
      }
    }

    // Clear errors
    if (errors[field]) {
      setErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };
  const updateFormData = (updates: Partial<UpdatePaymentMethodRequest>) => {
    setFormData((prev: Partial<UpdatePaymentMethodRequest>) => ({
      ...prev,
      ...updates,
    }));
  };

  const clearError = (field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  };

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (paymentMethod?.type === 'bank') {
      if (!formData.bank_code) {
        newErrors.bank_code = 'Please select a bank';
      }
      if (!formData.account_number?.trim()) {
        newErrors.account_number = 'Account number is required';
      }
      if (!formData.account_name?.trim()) {
        newErrors.account_name = 'Account name is required';
      }

      // Check if bank/account details changed and need validation
      const bankChanged = formData.bank_code !== paymentMethod.bank_code;
      const accountChanged =
        formData.account_number !== paymentMethod.account_number;

      if ((bankChanged || accountChanged) && !accountValidated) {
        newErrors.account_number = 'Please verify the account';
      }
    } else {
      if (!formData.crypto_network?.trim()) {
        newErrors.crypto_network = 'Crypto network is required';
      }
      if (!formData.wallet_address?.trim()) {
        newErrors.wallet_address = 'Wallet address is required';
      }
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent, onSuccess?: () => void) => {
    e.preventDefault();

    if (!paymentMethod || !validateForm()) {
      return;
    }

    try {
      setSaving(true);
      const response = await paymentMethodService.updatePaymentMethod(
        paymentMethod.id,
        formData as UpdatePaymentMethodRequest
      );

      if (response.status === 'success') {
        toast.success('Payment method updated successfully');
        onSuccess?.() || navigate('/payment-methods');
      } else {
        toast.error(response.message || 'Failed to update payment method');
      }
    } catch (error) {
      console.error('Error updating payment method:', error);
      toast.error('Failed to update payment method');
    } finally {
      setSaving(false);
    }
  };

  const needsValidation =
    paymentMethod?.type === 'bank' &&
    (formData.bank_code !== paymentMethod.bank_code ||
      formData.account_number !== paymentMethod.account_number) &&
    !accountValidated;
  return {
    paymentMethod,
    loading,
    saving,
    accountValidated,
    needsValidation,
    formData,
    errors,
    handleInputChange,
    updateFormData,
    clearError,
    validateForm,
    handleSubmit,
    setAccountValidated,
  };
}
