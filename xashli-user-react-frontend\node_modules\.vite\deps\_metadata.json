{"hash": "6ef8eca2", "configHash": "1b4e83bf", "lockfileHash": "7f0f45e5", "browserHash": "1ef67112", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "daa063d8", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "18d7e214", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "b360db4b", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "13755701", "needsInterop": true}, "@hookform/resolvers/zod": {"src": "../../@hookform/resolvers/zod/dist/zod.mjs", "file": "@hookform_resolvers_zod.js", "fileHash": "f03b4298", "needsInterop": false}, "@paystack/inline-js": {"src": "../../@paystack/inline-js/es/inline.js", "file": "@paystack_inline-js.js", "fileHash": "baa9d82a", "needsInterop": false}, "@solana/web3.js": {"src": "../../@solana/web3.js/lib/index.browser.esm.js", "file": "@solana_web3__js.js", "fileHash": "d98b43e8", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "2bfd9fe7", "needsInterop": false}, "axios": {"src": "../../axios/index.js", "file": "axios.js", "fileHash": "952811a2", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "8fe4b38b", "needsInterop": false}, "js-cookie": {"src": "../../js-cookie/dist/js.cookie.mjs", "file": "js-cookie.js", "fileHash": "e210a3d5", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "8750d9f2", "needsInterop": false}, "moment": {"src": "../../moment/dist/moment.js", "file": "moment.js", "fileHash": "c9b9d976", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "7ab45d88", "needsInterop": true}, "react-hook-form": {"src": "../../react-hook-form/dist/index.esm.mjs", "file": "react-hook-form.js", "fileHash": "bde704e2", "needsInterop": false}, "react-hot-toast": {"src": "../../react-hot-toast/dist/index.mjs", "file": "react-hot-toast.js", "fileHash": "f4b11099", "needsInterop": false}, "react-router-dom": {"src": "../../react-router-dom/dist/index.mjs", "file": "react-router-dom.js", "fileHash": "382c7ac6", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "e0da6c60", "needsInterop": false}, "zod": {"src": "../../zod/dist/esm/index.js", "file": "zod.js", "fileHash": "39fe32fe", "needsInterop": false}}, "chunks": {"chunk-WV6HCDVQ": {"file": "chunk-WV6HCDVQ.js"}, "chunk-2OEBGKUT": {"file": "chunk-2OEBGKUT.js"}, "chunk-D3OMFVKC": {"file": "chunk-D3OMFVKC.js"}, "chunk-5LFKFUIN": {"file": "chunk-5LFKFUIN.js"}, "chunk-OL46QLBJ": {"file": "chunk-OL46QLBJ.js"}}}