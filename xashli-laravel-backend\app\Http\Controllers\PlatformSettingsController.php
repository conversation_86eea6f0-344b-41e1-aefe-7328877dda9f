<?php

namespace App\Http\Controllers;

use App\Models\AdminActivityLog;
use App\Models\PaymentMatchingCycle;
use App\Models\PlatformSetting;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class PlatformSettingsController extends Controller
{
    /**
     * Display a listing of the platform settings.
     */
    public function index(): JsonResponse
    {
        $settings = PlatformSetting::all();

        $formattedSettings = [];
        foreach ($settings as $setting) {
            $formattedSettings[] = [
                'id' => $setting->id,
                'key' => $setting->key,
                'value' => $setting->getTypedValue(),
                'type' => $setting->type,
                'description' => $setting->description,
                'updated_at' => $setting->updated_at,
            ];
        }

        return $this->success($formattedSettings, 'Platform settings retrieved successfully');
    }

    /**
     * Store a newly created platform setting in storage.
     */
    public function store(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'key' => 'required|string|max:255|unique:platform_settings,key',
            'value' => 'required|string',
            'type' => 'required|in:string,int,decimal,boolean',
            'description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        // Additional validation for percentage settings
        if ($this->isPercentageSetting($request->key)) {
            $percentageValidator = Validator::make($request->all(), [
                'value' => 'numeric|between:0,100',
            ], [
                'value.between' => 'Percentage value must be between 0 and 100',
                'value.numeric' => 'Percentage value must be a valid number',
            ]);

            if ($percentageValidator->fails()) {
                return $this->validationError($percentageValidator->errors()->toArray());
            }
        }

        try {
            DB::beginTransaction();

            $setting = PlatformSetting::create([
                'id' => Str::uuid(),
                'key' => $request->key,
                'value' => $request->value,
                'type' => $request->type,
                'description' => $request->description,
                'updated_at' => now(),
            ]);

            // Log the activity
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_SETTINGS_CREATED,
                null,
                [
                    'setting_key' => $setting->key,
                    'setting_value' => $setting->value,
                    'setting_type' => $setting->type,
                    'setting_description' => $setting->description,
                ]
            );

            DB::commit();

            return $this->success([
                'id' => $setting->id,
                'key' => $setting->key,
                'value' => $setting->getTypedValue(),
                'type' => $setting->type,
                'description' => $setting->description,
                'updated_at' => $setting->updated_at,
            ], 'Platform setting created successfully', 201);
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to create platform setting: ' . $e->getMessage());
        }
    }

    /**
     * Display the specified platform setting.
     */
    public function show(string $id): JsonResponse
    {
        $setting = PlatformSetting::find($id);

        if (! $setting) {
            return $this->notFound('Platform setting not found');
        }

        return $this->success([
            'id' => $setting->id,
            'key' => $setting->key,
            'value' => $setting->getTypedValue(),
            'type' => $setting->type,
            'description' => $setting->description,
            'updated_at' => $setting->updated_at,
        ], 'Platform setting retrieved successfully');
    }

    /**
     * Update the specified platform setting in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $setting = PlatformSetting::find($id);

        if (! $setting) {
            return $this->notFound('Platform setting not found');
        }

        $validator = Validator::make($request->all(), [
            'key' => 'sometimes|string|max:255|unique:platform_settings,key,' . $id . ',id',
            'value' => 'sometimes|string',
            'type' => 'sometimes|in:string,int,decimal,boolean',
            'description' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return $this->validationError($validator->errors()->toArray());
        }

        // Additional validation for specific percentage settings
        if ($request->has('value') && $this->isPercentageSetting($setting->key)) {
            $percentageValidator = Validator::make($request->all(), [
                'value' => 'numeric|between:0,100',
            ], [
                'value.between' => 'Percentage value must be between 0 and 100',
                'value.numeric' => 'Percentage value must be a valid number',
            ]);

            if ($percentageValidator->fails()) {
                return $this->validationError($percentageValidator->errors()->toArray());
            }
        }

        try {
            DB::beginTransaction();

            $oldValue = $setting->value;
            $oldType = $setting->type;

            if ($request->has('key')) {
                $setting->key = $request->key;
            }

            if ($request->has('value')) {
                $setting->value = $request->value;
            }

            if ($request->has('type')) {
                $setting->type = $request->type;
            }

            if ($request->has('description')) {
                $setting->description = $request->description;
            }

            $setting->updated_at = now();
            $setting->save();

            // Update matching cycle if auto_match_frequency was changed
            if ($setting->key === 'auto_match_frequency' && $request->has('value')) {
                $matchingCycle = PaymentMatchingCycle::where('mode', 'auto')->first();
                if ($matchingCycle) {
                    $matchingCycle->frequency_hours = (int) $request->value;
                    $matchingCycle->updated_at = now();
                    $matchingCycle->save();
                }
            }

            // Log the activity
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_SETTINGS_UPDATED,
                null,
                [
                    'setting_key' => $setting->key,
                    'old_value' => $oldValue,
                    'new_value' => $setting->value,
                    'old_type' => $oldType,
                    'new_type' => $setting->type,
                ]
            );

            DB::commit();

            return $this->success([
                'id' => $setting->id,
                'key' => $setting->key,
                'value' => $setting->getTypedValue(),
                'type' => $setting->type,
                'description' => $setting->description,
                'updated_at' => $setting->updated_at,
            ], 'Platform setting updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to update platform setting: ' . $e->getMessage());
        }
    }

    /**
     * Remove the specified platform setting from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        $setting = PlatformSetting::find($id);

        if (! $setting) {
            return $this->notFound('Platform setting not found');
        }

        // Check if this is a core setting that shouldn't be deleted
        $coreSetting = in_array($setting->key, [
            'platform_fee_percentage',
            'fiat_growth_rate',
            'crypto_growth_rate',
            'auto_match_frequency',
            'auto_matching_enabled',
            'fast_maturity_days',
            'standard_maturity_days',
            'referral_level1_percentage',
            'referral_level2_percentage',
            'referral_level3_percentage',
            'fund_confirmation_timeout_hours',
            'fiat_fast_maturity_threshold',
            'crypto_fast_maturity_threshold',
            'minimum_fiat_fund_amount',
            'maximum_fiat_fund_amount',
            'minimum_crypto_fund_amount',
            'maximum_crypto_fund_amount',
            'elite_level_1_priviledge_maximum_fund_multiplier',
            'elite_level_2_priviledge_maximum_fund_multiplier',
            'elite_level_3_priviledge_maximum_fund_multiplier',
            'elite_level_4_priviledge_maximum_fund_multiplier',
            'elite_level_1_transaction_threshold',
            'elite_level_2_transaction_threshold',
            'elite_level_3_transaction_threshold',
            'elite_level_4_transaction_threshold',
            'elite_level_1_referee_threshold',
            'elite_level_2_referee_threshold',
            'elite_level_3_referee_threshold',
            'elite_level_4_referee_threshold',
        ]);

        if ($coreSetting) {
            return $this->error('Cannot delete core platform setting. Use update instead.', 400);
        }

        try {
            DB::beginTransaction();

            // Log the activity before deleting
            AdminActivityLog::log(
                auth()->user(),
                AdminActivityLog::ACTION_SETTINGS_DELETED,
                null,
                [
                    'setting_key' => $setting->key,
                    'setting_value' => $setting->value,
                    'setting_type' => $setting->type,
                    'setting_description' => $setting->description,
                ]
            );

            $setting->delete();

            DB::commit();

            return $this->success(null, 'Platform setting deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverError('Failed to delete platform setting: ' . $e->getMessage());
        }
    }

    /**
     * Check if the given setting key represents a percentage value.
     */
    private function isPercentageSetting(string $key): bool
    {
        $percentageSettings = [
            'platform_fee_percentage',
            'fiat_growth_rate',
            'crypto_growth_rate',
            'referral_level1_percentage',
            'referral_level2_percentage',
            'referral_level3_percentage',
        ];

        return in_array($key, $percentageSettings);
    }
}
