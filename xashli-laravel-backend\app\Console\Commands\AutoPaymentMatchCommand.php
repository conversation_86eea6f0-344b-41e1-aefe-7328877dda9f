<?php

namespace App\Console\Commands;

use App\Models\PaymentMatchingCycle;
use App\Models\PlatformSetting;
use App\Services\PaymentMatchingService;
use App\Services\PaymentMatchTimeoutService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class AutoPaymentMatchCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:auto-payment-match';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Automatically create payment matches between pending funds and withdraws';

    protected $paymentMatchingService;

    protected $paymentMatchTimeoutService;

    protected $paymentMatchingCycle;

    public function __construct(PaymentMatchingService $paymentMatchingService, PaymentMatchTimeoutService $paymentMatchTimeoutService)
    {
        parent::__construct();
        $this->paymentMatchingService = $paymentMatchingService;
        $this->paymentMatchTimeoutService = $paymentMatchTimeoutService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting automatic payment matching process...');

        // Check if auto matching is enabled
        $autoMatchingEnabled = PlatformSetting::getSetting('auto_payment_match_enabled');
        if (! $autoMatchingEnabled) {
            $this->warn('Automatic payment matching is disabled in platform settings.');

            return 0;
        }

        // Check if it's time to run the payment matching cycle
        $this->paymentMatchingCycle = PaymentMatchingCycle::where('mode', 'auto')->first();
        if (! $this->paymentMatchingCycle) {
            $this->error('No automatic payment matching cycle found.');

            return 0;
        }

        if (! $this->paymentMatchingCycle->isActive()) {
            $this->warn('Automatic payment matching cycle is inactive.');

            return 0;
        }

        if (! $this->paymentMatchingCycle->isDue()) {
            $this->info('Automatic payment matching cycle is not due yet. Next run: ' . $this->paymentMatchingCycle->next_run);

            return 0;
        }

        $this->info('Payment matching cycle is due. Processing payment matches...');

        try {
            // First, process any timed out payment matches
            $this->info('Processing payment match timeouts before matching...');
            $timeoutStats = $this->paymentMatchTimeoutService->processTimeouts();

            if ($timeoutStats['processed_matches'] > 0) {
                $this->info("Timeout processing completed: {$timeoutStats['deactivated_users']} users deactivated, {$timeoutStats['cancelled_matches']} matches cancelled");
            } else {
                $this->info('No timed out matches found.');
            }

            // Then proceed with automatic matching
            $this->info('Executing automatic payment matching...');
            $matchesCreated = $this->paymentMatchingService->executeMatching();

            // Update the next run time for the payment matching cycle
            $this->paymentMatchingCycle->updateNextRun();

            $this->info("Automatic payment matching completed. Created {$matchesCreated} payment matches.");

            return 0;
        } catch (\Exception $e) {
            $this->error('Error in automatic payment matching process: ' . $e->getMessage());
            Log::error('Auto match process error: ' . $e->getMessage());

            return 1;
        }
    }
}
