import { useEffect } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>gle, CreditCard, Wallet } from 'lucide-react';
import { Button } from '../ui/Button';
import { Modal } from '../ui/Modal';
import { ConfirmationCodeSection } from '../ui/ConfirmationCodeSection';
import { useConfirmationCode } from '../../hooks/useConfirmationCode';
import type { PaymentMethod } from '../../types';

interface DeletePaymentMethodDialogProps {
  open: boolean;
  paymentMethod: PaymentMethod | null;
  onConfirm: (paymentMethod: PaymentMethod, confirmationCode: string) => void;
  onCancel: () => void;
}

export function DeletePaymentMethodDialog({
  open,
  paymentMethod,
  onConfirm,
  onCancel,
}: DeletePaymentMethodDialogProps) {
  // Confirmation code hook
  const confirmationCode = useConfirmationCode({
    action: 'delete',
    context: 'payment_method',
  });

  // Reset confirmation code when dialog opens/closes
  useEffect(() => {
    if (!open) {
      confirmationCode.reset();
    }
  }, [open, confirmationCode.reset]);

  // Early return after all hooks
  if (!paymentMethod) return null;

  const isBankAccount = paymentMethod.type === 'bank';

  const handleConfirm = () => {
    if (confirmationCode.isCodeValid) {
      onConfirm(paymentMethod, confirmationCode.code);
    }
  };

  return (
    <Modal
      isOpen={open}
      onClose={onCancel}
      title='Delete Payment Method'
      size='sm'
    >
      <div className='flex items-center gap-3 mb-4'>
        <div className='p-2 bg-destructive/10 rounded-lg'>
          <AlertTriangle className='h-5 w-5 text-destructive' />
        </div>
        <div>
          <p className='text-foreground-secondary'>
            Are you sure you want to delete this payment method? This action
            cannot be undone.
          </p>
        </div>
      </div>

      {/* Payment Method Details */}
      <div className='bg-background-secondary rounded-lg p-4 space-y-2 mb-6'>
        <div className='flex items-center gap-2 text-sm font-medium'>
          {isBankAccount ? (
            <CreditCard className='h-4 w-4' />
          ) : (
            <Wallet className='h-4 w-4' />
          )}
          {isBankAccount
            ? `${paymentMethod.account_name}`
            : `${paymentMethod.crypto_network} Wallet`}
        </div>

        {isBankAccount ? (
          <div className='text-sm text-foreground-secondary space-y-1'>
            <div>{paymentMethod.bank_name}</div>
            <div className='font-mono'>
              {paymentMethod.account_number}
            </div>
            <div>{paymentMethod.account_name}</div>
          </div>
        ) : (
          <div className='text-sm text-foreground-secondary space-y-1'>
            <div>{paymentMethod.crypto_network}</div>
            <div className='font-mono text-xs'>
              {paymentMethod.wallet_address?.slice(0, 12)}...
              {paymentMethod.wallet_address?.slice(-12)}
            </div>
          </div>
        )}
      </div>

      {/* Confirmation Code Section */}
      <ConfirmationCodeSection
        code={confirmationCode.code}
        onCodeChange={confirmationCode.setCode}
        onRequestCode={confirmationCode.requestCode}
        isRequesting={confirmationCode.isRequesting}
        isRequested={confirmationCode.isRequested}
        canRequest={confirmationCode.canRequest}
        cooldownRemaining={confirmationCode.cooldownRemaining}
        error={confirmationCode.error}
        title='Security Confirmation Required'
        description='For your security, please request and enter a confirmation code to delete this payment method.'
        className='mb-6'
      />

      {/* Footer Buttons */}
      <div className='flex gap-3'>
        <Button variant='outline' onClick={onCancel} className='flex-1'>
          Cancel
        </Button>
        <Button
          variant='destructive'
          onClick={handleConfirm}
          disabled={!confirmationCode.isCodeValid}
          className='flex-1'
        >
          Delete Payment Method
        </Button>
      </div>
    </Modal>
  );
}
