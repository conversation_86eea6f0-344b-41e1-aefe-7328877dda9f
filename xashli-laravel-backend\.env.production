APP_NAME=Xashli
APP_ENV=production
APP_KEY=base64:Oizl7FdiZgP3PqOze6AQBypbHcKYSdsyZ6v59uv05Jc=
APP_DEBUG=false
APP_URL=https://api.xashli.com
APP_FRONTEND_URL=https://xashli.com
APP_ADMIN_FRONTEND_URL=https://admin.xashli.com

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=error

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=u170988394_xashli
DB_USERNAME=u170988394_SystemAdmin
DB_PASSWORD=SysAd@12345!

# Sessions disabled for API-only backend (using JWT authentication)
SESSION_DRIVER=array
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=.xashli.com
SESSION_SECURE_COOKIE=true
SESSION_SAME_SITE=lax

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
CACHE_PREFIX=xashli_cache

MEMCACHED_HOST=127.0.0.1

# Redis Configuration (not used)
# REDIS_CLIENT=phpredis
# REDIS_HOST=127.0.0.1
# REDIS_PASSWORD=SECURE_REDIS_PASSWORD
# REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=smtp.hostinger.com
MAIL_PORT=587
MAIL_ENCRYPTION=tls
MAIL_USERNAME="<EMAIL>"
MAIL_PASSWORD="SysAd@12345!"
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

# Termii SMS Configuration
TERMII_API_KEY=TLDzCPorAgyhgkJbEMOpOEEFLmGstoNZCoWIkvAlGxBndvpepSSXlUIJxaKgvs
TERMII_SENDER_ID=Xashli
TERMII_CHANNEL=generic

# Paystack Configuration - LIVE KEYS
PAYSTACK_SECRET_KEY=************************************************
PAYSTACK_PUBLIC_KEY=pk_live_348a2b6acb9f990f5f45f9fff275ec2f1d245c99
PAYSTACK_BASE_URL=https://api.paystack.co

JWT_SECRET=79qP1d0tdlek36uDAZChulnSv0jp0CIJYZWntpU0uVXqngrxckWUV9GQ0rFm6Dwo
JWT_TTL=1440

# Additional Production Settings
TELESCOPE_ENABLED=false
DEBUGBAR_ENABLED=false
