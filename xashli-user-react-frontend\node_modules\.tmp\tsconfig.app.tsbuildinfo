{"root": ["../../src/app.tsx", "../../src/main.tsx", "../../src/vite-env.d.ts", "../../src/components/protectedroute.tsx", "../../src/components/auth/privacypolicymodal.tsx", "../../src/components/auth/termsofusemodal.tsx", "../../src/components/auth/index.ts", "../../src/components/disputes/disputemodal.tsx", "../../src/components/funds/cancelfunddialog.tsx", "../../src/components/funds/fundcard.tsx", "../../src/components/funds/fundfilters.tsx", "../../src/components/funds/fundpaymentmatchcard.tsx", "../../src/components/funds/fundstatistics.tsx", "../../src/components/funds/paymentmodal.tsx", "../../src/components/funds/index.ts", "../../src/components/layouts/applayout.tsx", "../../src/components/layouts/authlayout.tsx", "../../src/components/layouts/sidebar.tsx", "../../src/components/layouts/topbar.tsx", "../../src/components/layouts/index.ts", "../../src/components/payment-methods/deletepaymentmethoddialog.tsx", "../../src/components/payment-methods/paymentmethodcard.tsx", "../../src/components/payment-methods/index.ts", "../../src/components/referrals/refereeslist.tsx", "../../src/components/referrals/referralbonuses.tsx", "../../src/components/referrals/referraloverview.tsx", "../../src/components/referrals/index.ts", "../../src/components/ui/accordion.tsx", "../../src/components/ui/alert.tsx", "../../src/components/ui/badge.tsx", "../../src/components/ui/button.tsx", "../../src/components/ui/card.tsx", "../../src/components/ui/confirmationcodesection.tsx", "../../src/components/ui/deactivateduseralert.tsx", "../../src/components/ui/input.tsx", "../../src/components/ui/label.tsx", "../../src/components/ui/loadingspinner.tsx", "../../src/components/ui/modal.tsx", "../../src/components/ui/select.tsx", "../../src/components/ui/textarea.tsx", "../../src/components/ui/dropdown-menu.tsx", "../../src/components/ui/index.ts", "../../src/components/ui/pagination.tsx", "../../src/components/withdraws/withdrawcard.tsx", "../../src/components/withdraws/withdrawfilters.tsx", "../../src/components/withdraws/withdrawpaymentmatchcard.tsx", "../../src/components/withdraws/withdrawsstatistics.tsx", "../../src/components/withdraws/index.ts", "../../src/contexts/authcontext.tsx", "../../src/contexts/sidebarcontext.tsx", "../../src/contexts/index.ts", "../../src/data/privileges.ts", "../../src/hooks/index.ts", "../../src/hooks/useconfirmationcode.ts", "../../src/hooks/usedashboardstats.ts", "../../src/hooks/auth/index.ts", "../../src/hooks/auth/useforgotpassword.ts", "../../src/hooks/auth/uselogin.ts", "../../src/hooks/auth/usepasswordrequirements.ts", "../../src/hooks/auth/usepasswordvisibility.ts", "../../src/hooks/auth/useregister.ts", "../../src/hooks/auth/useresetpassword.ts", "../../src/hooks/auth/useverification.ts", "../../src/hooks/dashboard/usedashboardstats.ts", "../../src/hooks/funds/index.ts", "../../src/hooks/funds/usefund.ts", "../../src/hooks/funds/usefundform.ts", "../../src/hooks/funds/usefundstatistics.ts", "../../src/hooks/funds/usefunds.ts", "../../src/hooks/funds/usepaystackpopup.ts", "../../src/hooks/payment-methods/index.ts", "../../src/hooks/payment-methods/useaccountvalidation.ts", "../../src/hooks/payment-methods/usebanks.ts", "../../src/hooks/payment-methods/useconfirmationcode.ts", "../../src/hooks/payment-methods/useeditpaymentmethod.ts", "../../src/hooks/payment-methods/usepaymentmethod.ts", "../../src/hooks/payment-methods/usepaymentmethodform.ts", "../../src/hooks/payment-methods/usepaymentmethods.ts", "../../src/hooks/profile/index.ts", "../../src/hooks/profile/useprofile.ts", "../../src/hooks/profile/useprofileimageupload.ts", "../../src/hooks/referrals/index.ts", "../../src/hooks/referrals/usereferrals.ts", "../../src/hooks/withdraws/index.ts", "../../src/hooks/withdraws/usewithdraw.ts", "../../src/hooks/withdraws/usewithdrawstatistics.ts", "../../src/hooks/withdraws/usewithdraws.ts", "../../src/pages/dashboardpage.tsx", "../../src/pages/homepage.tsx", "../../src/pages/auth/forgotpasswordpage.tsx", "../../src/pages/auth/loginpage.tsx", "../../src/pages/auth/registerpage.tsx", "../../src/pages/auth/resetpasswordpage.tsx", "../../src/pages/auth/verificationpage.tsx", "../../src/pages/funds/createfundpage.tsx", "../../src/pages/funds/funddetailspage.tsx", "../../src/pages/funds/fundstatisticspage.tsx", "../../src/pages/funds/fundspage.tsx", "../../src/pages/funds/index.ts", "../../src/pages/payment-methods/createpaymentmethodpage.tsx", "../../src/pages/payment-methods/editpaymentmethodpage.tsx", "../../src/pages/payment-methods/paymentmethoddetailspage.tsx", "../../src/pages/payment-methods/paymentmethodspage.tsx", "../../src/pages/profile/editprofilepage.tsx", "../../src/pages/profile/profilepage.tsx", "../../src/pages/profile/index.ts", "../../src/pages/referrals/referralbonusespage.tsx", "../../src/pages/referrals/referralspage.tsx", "../../src/pages/referrals/index.ts", "../../src/pages/withdraws/withdrawdetailspage.tsx", "../../src/pages/withdraws/withdrawstatisticspage.tsx", "../../src/pages/withdraws/withdrawspage.tsx", "../../src/pages/withdraws/index.ts", "../../src/schemas/profile.ts", "../../src/services/api.ts", "../../src/services/auth.ts", "../../src/services/confirmation.ts", "../../src/services/dashboard.ts", "../../src/services/dispute.ts", "../../src/services/fund.ts", "../../src/services/index.ts", "../../src/services/paymentmatch.ts", "../../src/services/paymentmethod.ts", "../../src/services/paystack.ts", "../../src/services/profile.ts", "../../src/services/referral.ts", "../../src/services/verification.ts", "../../src/services/withdraw.ts", "../../src/types/auth.ts", "../../src/types/common.ts", "../../src/types/confirmation.ts", "../../src/types/dashboard.ts", "../../src/types/dispute.ts", "../../src/types/fund.ts", "../../src/types/index.ts", "../../src/types/paymentmatch.ts", "../../src/types/paymentmethod.ts", "../../src/types/paystack-inline.d.ts", "../../src/types/paystack.ts", "../../src/types/profile.ts", "../../src/types/referral.ts", "../../src/types/verification.ts", "../../src/types/withdraw.ts", "../../src/utils/cn.ts", "../../src/utils/convert.ts", "../../src/utils/copy.ts", "../../src/utils/format.ts", "../../src/utils/images.ts", "../../src/utils/toast.ts", "../../src/utils/utils.ts", "../../src/utils/web3.ts"], "errors": true, "version": "5.8.3"}